<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="37e79307-b2a1-4c86-a6ee-8753af03bbc6" name="Changes" comment="Sửa lỗi format code&#10;dauthau.info#3441#3442">
      <change beforePath="$PROJECT_DIR$/src/.htaccess" beforeDir="false" afterPath="$PROJECT_DIR$/src/.htaccess" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/src/includes/composer.json" />
      <option value="$PROJECT_DIR$/src/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="master" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev_hai_3441_3442" />
                    <option name="lastUsedInstant" value="1748942497" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1748942448" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev_hai_3418" />
                    <option name="lastUsedInstant" value="1748508582" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev_hai_3341_3342" />
                    <option name="lastUsedInstant" value="1748502840" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev_hai_3384" />
                    <option name="lastUsedInstant" value="1748448622" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\xampp\php\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/src/includes/vendor/league/oauth2-client" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/src/includes/vendor/smarty/smarty" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/src/includes/vendor/tecnickcom/tc-lib-barcode" />
      <path value="$PROJECT_DIR$/src/includes/vendor/tecnickcom/tc-lib-color" />
      <path value="$PROJECT_DIR$/src/includes/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/vinades/nukeviet" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/composer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/vinades/pclzip" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/src/includes/vendor/zaloplatform/zalo-php-sdk" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/src/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/src/vendor/jasny/sso" />
      <path value="$PROJECT_DIR$/src/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/src/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/src/vendor/jasny/validation-result" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/src/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/src/vendor/elastic/transport" />
      <path value="$PROJECT_DIR$/src/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/src/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/src/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/src/vendor/elasticsearch/elasticsearch" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/discovery" />
      <path value="$PROJECT_DIR$/src/vendor/voku/anti-xss" />
      <path value="$PROJECT_DIR$/src/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/httplug" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/promise" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/src/vendor/voku/portable-utf8" />
      <path value="$PROJECT_DIR$/src/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/src/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-sdk-php-resources" />
      <path value="$PROJECT_DIR$/src/vendor/composer" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/src/vendor/desarrolla2/cache" />
      <path value="$PROJECT_DIR$/src/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/src/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/polyfill-iconv" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/serializer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/type-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-access" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/property-info" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/webauthn-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/web-auth/cose-lib" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/brick/math" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/container" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/src/includes/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/cbor-php" />
      <path value="$PROJECT_DIR$/src/includes/vendor/spomky-labs/pki-framework" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2uXUHhwpzAQ8CFhXeUUo8hkWmCA" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;PHP Script.arr_data_2.php.executor&quot;: &quot;Run&quot;,
    &quot;PHP Script.export_data_fomo.php.executor&quot;: &quot;Run&quot;,
    &quot;PHP Script.viewfomo.php.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev__hai__3441__3442&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/webroot/www/dauthau/api.dauthau.info&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;D:\\webroot\\www\\dauthau\\dauthau.info\\node_modules\\stylelint&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\webroot\www\dauthau\dauthau.info\src\themes\dauthau\modules\businesslistings" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-PS-242.23726.107" />
        <option value="bundled-php-predefined-a98d8de5180a-90914f2295cb-com.jetbrains.php.sharedIndexes-PS-242.23726.107" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="37e79307-b2a1-4c86-a6ee-8753af03bbc6" name="Changes" comment="" />
      <created>1742394329476</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742394329476</updated>
      <workItem from="1742394331523" duration="7704000" />
      <workItem from="1742443389581" duration="6304000" />
      <workItem from="1742464742269" duration="843000" />
      <workItem from="1742485035668" duration="2641000" />
      <workItem from="1742539830160" duration="7928000" />
      <workItem from="1742629128651" duration="8261000" />
      <workItem from="1742733158515" duration="603000" />
      <workItem from="1742778735669" duration="4501000" />
      <workItem from="1742805689114" duration="4838000" />
      <workItem from="1742887205350" duration="10999000" />
      <workItem from="1742973287919" duration="9063000" />
      <workItem from="1743060985375" duration="6035000" />
      <workItem from="1743143586810" duration="4949000" />
      <workItem from="1743433438973" duration="819000" />
      <workItem from="1743493723176" duration="8156000" />
      <workItem from="1743578463019" duration="8221000" />
      <workItem from="1743604305750" duration="2689000" />
      <workItem from="1743652037750" duration="18124000" />
      <workItem from="1743838570715" duration="5629000" />
      <workItem from="1744081799225" duration="2736000" />
      <workItem from="1744119984505" duration="67000" />
      <workItem from="1744120779493" duration="5453000" />
      <workItem from="1744132464499" duration="725000" />
      <workItem from="1744137300467" duration="305000" />
      <workItem from="1744164713647" duration="13021000" />
      <workItem from="1744192881290" duration="1434000" />
      <workItem from="1744431020611" duration="67000" />
      <workItem from="1744601760397" duration="672000" />
      <workItem from="1744643909287" duration="5355000" />
      <workItem from="1744702285394" duration="11575000" />
      <workItem from="1744741111191" duration="9000" />
      <workItem from="1744769193451" duration="11868000" />
      <workItem from="1744820012241" duration="598000" />
      <workItem from="1744875967027" duration="4908000" />
      <workItem from="1744882737330" duration="11000" />
      <workItem from="1744882954754" duration="634000" />
      <workItem from="1744904680246" duration="2842000" />
      <workItem from="1744940222235" duration="7736000" />
      <workItem from="1744949718516" duration="15000" />
      <workItem from="1744950078308" duration="13000" />
      <workItem from="1744950369013" duration="364000" />
      <workItem from="1744961793128" duration="4610000" />
      <workItem from="1744989613850" duration="2245000" />
      <workItem from="1745026726858" duration="8948000" />
      <workItem from="1745073805500" duration="1518000" />
      <workItem from="1745203267934" duration="2419000" />
      <workItem from="1745247712178" duration="674000" />
      <workItem from="1745293537873" duration="758000" />
      <workItem from="1745294804504" duration="10136000" />
      <workItem from="1745375873374" duration="2354000" />
      <workItem from="1745396042958" duration="977000" />
      <workItem from="1745421817273" duration="4341000" />
      <workItem from="1745464091165" duration="15144000" />
      <workItem from="1745553457060" duration="8697000" />
      <workItem from="1745641553404" duration="13382000" />
      <workItem from="1745808814394" duration="1224000" />
      <workItem from="1745823939097" duration="1222000" />
      <workItem from="1745889731893" duration="592000" />
      <workItem from="1745894409815" duration="4289000" />
      <workItem from="1746501811135" duration="6249000" />
      <workItem from="1746585882860" duration="585000" />
      <workItem from="1746671543097" duration="40000" />
      <workItem from="1746762307460" duration="150000" />
      <workItem from="1746773792546" duration="1999000" />
      <workItem from="1746801705447" duration="5361000" />
      <workItem from="1746810291661" duration="351000" />
      <workItem from="1746947401392" duration="1011000" />
      <workItem from="1746974129874" duration="8091000" />
      <workItem from="1747099039737" duration="1386000" />
      <workItem from="1747120214120" duration="4271000" />
      <workItem from="1747128482403" duration="11558000" />
      <workItem from="1747190445167" duration="6783000" />
      <workItem from="1747209859604" duration="315000" />
      <workItem from="1747295138165" duration="1242000" />
      <workItem from="1747324952639" duration="209000" />
      <workItem from="1747363165417" duration="3935000" />
      <workItem from="1747641215098" duration="352000" />
      <workItem from="1747712478830" duration="4139000" />
      <workItem from="1747757072130" duration="241000" />
      <workItem from="1747813391159" duration="81000" />
      <workItem from="1747818764093" duration="6166000" />
      <workItem from="1747931551438" duration="1006000" />
      <workItem from="1747984878543" duration="603000" />
      <workItem from="1747988619613" duration="788000" />
      <workItem from="1747989485944" duration="270000" />
      <workItem from="1747990962943" duration="5000" />
      <workItem from="1748245297287" duration="1696000" />
      <workItem from="1748315811635" duration="255000" />
      <workItem from="1748335799328" duration="29000" />
      <workItem from="1748448514666" duration="2360000" />
      <workItem from="1748489655113" duration="5191000" />
      <workItem from="1748536720788" duration="2165000" />
      <workItem from="1748589809924" duration="146000" />
      <workItem from="1748705228984" duration="1856000" />
      <workItem from="1748849937756" duration="4944000" />
      <workItem from="1748867242570" duration="578000" />
      <workItem from="1748932092450" duration="2644000" />
    </task>
    <task id="LOCAL-00043" summary="Vẽ biểu đồ top 10 cho VNR500 tại trang thống kê top 10 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744127920028</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1744127920028</updated>
    </task>
    <task id="LOCAL-00044" summary="Vẽ biểu đồ top 10 cho VNR500 tại trang thống kê top 10 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744137357885</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1744137357885</updated>
    </task>
    <task id="LOCAL-00045" summary="Vẽ biểu đồ top 10 cho VNR500 tại trang thống kê top 10 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744183528911</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1744183528911</updated>
    </task>
    <task id="LOCAL-00046" summary="Vẽ biểu đồ top 10 cho VNR500 tại trang thống kê top 10 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744184458108</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1744184458108</updated>
    </task>
    <task id="LOCAL-00047" summary="Fix conflict dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744184659489</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1744184659489</updated>
    </task>
    <task id="LOCAL-00048" summary="Fix conflict dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744184764802</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1744184764802</updated>
    </task>
    <task id="LOCAL-00049" summary="Fix conflict dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744185686133</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1744185686133</updated>
    </task>
    <task id="LOCAL-00050" summary="Fix conflict dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744186069936</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1744186069937</updated>
    </task>
    <task id="LOCAL-00051" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744188377029</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1744188377029</updated>
    </task>
    <task id="LOCAL-00052" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744645766695</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1744645766695</updated>
    </task>
    <task id="LOCAL-00053" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744737515733</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1744737515735</updated>
    </task>
    <task id="LOCAL-00054" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744787812779</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1744787812783</updated>
    </task>
    <task id="LOCAL-00055" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744787820713</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1744787820713</updated>
    </task>
    <task id="LOCAL-00056" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744788032893</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1744788032893</updated>
    </task>
    <task id="LOCAL-00057" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744788597929</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1744788597929</updated>
    </task>
    <task id="LOCAL-00058" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744788698193</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1744788698193</updated>
    </task>
    <task id="LOCAL-00059" summary="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744790208466</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1744790208466</updated>
    </task>
    <task id="LOCAL-00060" summary="Fix conflict dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744882105024</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1744882105024</updated>
    </task>
    <task id="LOCAL-00061" summary="Fix conflict dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744882267468</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1744882267468</updated>
    </task>
    <task id="LOCAL-00062" summary="Fix conflict dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744882397123</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1744882397123</updated>
    </task>
    <task id="LOCAL-00063" summary="Fix conflict dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1744883265086</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1744883265086</updated>
    </task>
    <task id="LOCAL-00064" summary="Tối ưu biểu đồ dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1745203967523</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1745203967523</updated>
    </task>
    <task id="LOCAL-00065" summary="Tối ưu biểu đồ dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1745248146761</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1745248146762</updated>
    </task>
    <task id="LOCAL-00066" summary="[daugia.net] Trao đổi - Bỏ phần popup hiển thị thành viên đăng ký và thay đổi nội dung khi click vào dauthau.info#3340">
      <option name="closed" value="true" />
      <created>1745424541152</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1745424541152</updated>
    </task>
    <task id="LOCAL-00067" summary="Sửa link đăng kí thành viên dauthau.info#3340">
      <option name="closed" value="true" />
      <created>1745424733542</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1745424733542</updated>
    </task>
    <task id="LOCAL-00068" summary="Sửa link đăng kí thành viên dauthau.info#3340">
      <option name="closed" value="true" />
      <created>1745488853839</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1745488853839</updated>
    </task>
    <task id="LOCAL-00069" summary=" Sửa font chữ và sắp xếp lại top cho các nhà thầu trong bảng chú giải dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1745652025171</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1745652025171</updated>
    </task>
    <task id="LOCAL-00070" summary=" Sửa font chữ và sắp xếp lại top cho các nhà thầu trong bảng chú giải dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1745660945415</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1745660945415</updated>
    </task>
    <task id="LOCAL-00071" summary=" Sửa font chữ và sắp xếp lại top cho các nhà thầu trong bảng chú giải dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1745661989915</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1745661989915</updated>
    </task>
    <task id="LOCAL-00072" summary=" Sửa font chữ và sắp xếp lại top cho các nhà thầu trong bảng chú giải dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1745662093294</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1745662093294</updated>
    </task>
    <task id="LOCAL-00073" summary=" Sửa font chữ và sắp xếp lại top cho các nhà thầu trong bảng chú giải dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1746517149507</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1746517149507</updated>
    </task>
    <task id="LOCAL-00074" summary="Sửa lỗi mất biểu đồ và hiển thị tooltip chuẩn chỉnh dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1746805204706</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1746805204710</updated>
    </task>
    <task id="LOCAL-00075" summary="Thứ tự thời gian của KHLCNT đang bị sắp xếp lẫn lộn dauthau.info#3394">
      <option name="closed" value="true" />
      <created>1746979183449</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1746979183449</updated>
    </task>
    <task id="LOCAL-00076" summary="Sửa lỗi hiển thị tooltip dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1746983589882</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1746983589882</updated>
    </task>
    <task id="LOCAL-00077" summary="Sửa lỗi hiển thị tooltip dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1747152502853</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1747152502854</updated>
    </task>
    <task id="LOCAL-00078" summary="Sửa lỗi hiển thị tooltip dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1747157837098</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1747157837098</updated>
    </task>
    <task id="LOCAL-00079" summary="Sắp xếp thời gian dauthau.info#3394">
      <option name="closed" value="true" />
      <created>1747159478323</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1747159478323</updated>
    </task>
    <task id="LOCAL-00080" summary="Sửa lỗi tooltip hiển thị chưa mượt mà dauthau.info#3186">
      <option name="closed" value="true" />
      <created>1747196915751</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1747196915751</updated>
    </task>
    <task id="LOCAL-00081" summary="Chỉnh lại vị trí logo các site và bổ sung logo của BaoGia.Net vào hệ sinh thái&#10;dauthau.info#3418">
      <option name="closed" value="true" />
      <created>1747849835679</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1747849835680</updated>
    </task>
    <task id="LOCAL-00082" summary="Chỉnh lại vị trí logo các site và bổ sung logo của BaoGia.Net vào hệ sinh thái&#10;dauthau.info#3418">
      <option name="closed" value="true" />
      <created>1747850472508</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1747850472508</updated>
    </task>
    <task id="LOCAL-00083" summary="Chỉnh lại vị trí logo các site và bổ sung logo của BaoGia.Net vào hệ sinh thái&#10;dauthau.info#3418">
      <option name="closed" value="true" />
      <created>1747988715391</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1747988715391</updated>
    </task>
    <task id="LOCAL-00084" summary="Gửi thông báo cho khách khi có thông tin &quot;Phòng chào giá trực tuyến&quot; và &quot;Kết quả chào giá trực tuyến&quot;&#10;dauthau.info#3384">
      <option name="closed" value="true" />
      <created>1748448719606</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1748448719607</updated>
    </task>
    <task id="LOCAL-00085" summary="Thay đổi đường link sau khi tìm kiếm tỉnh thành tại trang TỈNH THÀNH &#10;dauthau.info#3341#3342">
      <option name="closed" value="true" />
      <created>1748537036385</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1748537036386</updated>
    </task>
    <task id="LOCAL-00086" summary="Thay đổi đường link sau khi tìm kiếm tỉnh thành tại trang TỈNH THÀNH &#10;dauthau.info#3341#3342">
      <option name="closed" value="true" />
      <created>1748538013920</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1748538013920</updated>
    </task>
    <task id="LOCAL-00087" summary="Thay đổi đường link sau khi tìm kiếm tỉnh thành tại trang TỈNH THÀNH &#10;dauthau.info#3441#3442">
      <option name="closed" value="true" />
      <created>1748705333281</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1748705333283</updated>
    </task>
    <task id="LOCAL-00088" summary="Thay đổi đường link sau khi tìm kiếm tỉnh thành tại trang TỈNH THÀNH &#10;dauthau.info#3441#3442">
      <option name="closed" value="true" />
      <created>1748705492236</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1748705492236</updated>
    </task>
    <task id="LOCAL-00089" summary="Sửa lỗi format code&#10;dauthau.info#3441#3442">
      <option name="closed" value="true" />
      <created>1748867657279</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1748867657280</updated>
    </task>
    <task id="LOCAL-00090" summary="Sửa lỗi format code&#10;dauthau.info#3441#3442">
      <option name="closed" value="true" />
      <created>1748942329759</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1748942329761</updated>
    </task>
    <task id="LOCAL-00091" summary="Sửa lỗi format code&#10;dauthau.info#3441#3442">
      <option name="closed" value="true" />
      <created>1748942850763</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1748942850763</updated>
    </task>
    <option name="localTasksCounter" value="92" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Vẽ biểu đồ top 10 cho VNR500 tại trang thống kê top 10 dauthau.info#3186" />
    <MESSAGE value="Xử lí issue biểu đồ top 10 vnr500 dauthau.info#3186" />
    <MESSAGE value="Fix conflict dauthau.info#3186" />
    <MESSAGE value="Tối ưu rewrite , lang dauthau.info#3186" />
    <MESSAGE value="Tối ưu biểu đồ dauthau.info#3186" />
    <MESSAGE value="[daugia.net] Trao đổi - Bỏ phần popup hiển thị thành viên đăng ký và thay đổi nội dung khi click vào dauthau.info#3340" />
    <MESSAGE value="Sửa link đăng kí thành viên dauthau.info#3340" />
    <MESSAGE value=" Sửa font chữ và sắp xếp lại top cho các nhà thầu trong bảng chú giải dauthau.info#3186" />
    <MESSAGE value="Sửa lỗi mất biểu đồ và hiển thị tooltip chuẩn chỉnh dauthau.info#3186" />
    <MESSAGE value="Thứ tự thời gian của KHLCNT đang bị sắp xếp lẫn lộn dauthau.info#3394" />
    <MESSAGE value="Sửa lỗi hiển thị tooltip dauthau.info#3186" />
    <MESSAGE value="Sắp xếp thời gian dauthau.info#3394" />
    <MESSAGE value="Sửa lỗi tooltip hiển thị chưa mượt mà dauthau.info#3186" />
    <MESSAGE value="Chỉnh lại vị trí logo các site và bổ sung logo của BaoGia.Net vào hệ sinh thái&#10;dauthau.info#3418" />
    <MESSAGE value="Gửi thông báo cho khách khi có thông tin &quot;Phòng chào giá trực tuyến&quot; và &quot;Kết quả chào giá trực tuyến&quot;&#10;dauthau.info#3384" />
    <MESSAGE value="Thay đổi đường link sau khi tìm kiếm tỉnh thành tại trang TỈNH THÀNH &#10;dauthau.info#3341#3342" />
    <MESSAGE value="Thay đổi đường link sau khi tìm kiếm tỉnh thành tại trang TỈNH THÀNH &#10;dauthau.info#3441#3442" />
    <MESSAGE value="Sửa lỗi format code&#10;dauthau.info#3441#3442" />
    <option name="LAST_COMMIT_MESSAGE" value="Sửa lỗi format code&#10;dauthau.info#3441#3442" />
  </component>
</project>