<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jul 06, 2011, 06:31:13 AM
 */
if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

use NukeViet\Dauthau\Url;
use NukeViet\Dauthau\Share;

if (!function_exists('nv_bidding_block_search')) {

    /**
     * nv_block_config_laws_search()
     *
     * @param mixed $module
     * @param mixed $data_block
     * @return
     */
    function nv_block_config_bidding_search($module, $data_block)
    {
        global $nv_Lang;

        $html = '';
        $html .= '<div class="form-group">';
        $html .= '<label class="control-label col-sm-6">' . $nv_Lang->getModule('style') . ':</label>';
        $html .= ' <div class="col-sm-9">';
        $html .= "<select name=\"config_style\" class=\"form-control\">\n";
        $sel = $data_block['style'] == 'center' ? 'selected="selected"' : '';
        $html .= '<option value="center" ' . $sel . '>Center</option>';
        $sel = $data_block['style'] == 'center_top' ? 'selected="selected"' : '';
        $html .= '<option value="center_top" ' . $sel . '>Center top</option>';
        $sel = $data_block['style'] == 'vertical' ? 'selected="selected"' : '';
        $html .= '<option value="vertical" ' . $sel . '>Vertical</option>';
        $html .= "</select>\n";
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * nv_block_config_laws_search_submit()
     *
     * @param mixed $module
     * @return
     */
    function nv_block_config_bidding_search_submit($module)
    {
        global $nv_Request;
        $return = array();
        $return['error'] = array();
        $return['config'] = array();
        $return['config']['style'] = $nv_Request->get_string('config_style', 'post', '');
        return $return;
    }

    /**
     * nv_law_block_search()
     *
     * @param mixed $block_config
     * @return
     */
    function nv_bidding_block_search($block_config)
    {
        global $my_head, $db, $site_mods, $global_config, $module_name, $module_config, $nv_Request, $nv_Cache, $op, $db_config, $array_op, $_array_phanmuc_bids, $nv_Lang, $loai_cong_trinhs, $user_info;
        $max_vaule_price = Share::MAX_VALUE_PRICE;
        $module = $block_config['module'];
        $mod_file = $site_mods[$module]['module_file'];
        $mod_data = $site_mods[$module]['module_data'];

        $module = $block_config['module'];
        $module_data = $site_mods[$module]['module_data'];
        $module_file = $site_mods[$module]['module_file'];
        $module_info = $site_mods[$module];

        $tbmt_alias = $module_info['alias']['view'] . '/' . Url::getTBMT();
        $alias_part = NV_LANG_DATA == 'vi' ? 'tinh-thanh' : 'tenderlistbylocation';
        $tbmt_alias_t = $module_info['alias']['view'] . '/' . Url::getTBMT() . '/' . $alias_part;
        $nv_Lang->changeLang();
        $nv_Lang->loadModule($module_file, false, true);

        if ($block_config['style'] == 'center') {
            $block_file_name = 'block_search_center.tpl';
        } else if ($block_config['style'] == 'center_top') {
            $block_file_name = 'block_search_center_top.tpl';
        } else if ($block_config['style'] == 'vertical') {
            $block_file_name = 'block_search_vertical.tpl';
        }

        if (file_exists(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/bidding/' . $block_file_name)) {
            $block_theme = $global_config['module_theme'];
        } else {
            $block_theme = 'default';
        }

        $xtpl = new XTemplate($block_file_name, NV_ROOTDIR . '/themes/' . $block_theme . '/modules/bidding');

        $xtpl->assign('LANG', \NukeViet\Core\Language::$tmplang_module);
        $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
        $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
        $xtpl->assign('NV_MY_DOMAIN', NV_MY_DOMAIN);
        $xtpl->assign('TEMPLATE', $block_theme);
        $xtpl->assign('MODULE_FILE', $module_file);
        $xtpl->assign('BLOCKID', $block_config['bid']);
        $xtpl->assign('TIMESTAMP', $global_config['timestamp']);

        if ($global_config['iddomain'] != 2) {
            $xtpl->parse('main.show_hashtag');
        }

        if ($module != $module_name) {
            if (file_exists(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/css/' . $mod_file . '.css')) {
                $block_css = $global_config['module_theme'];
            } elseif (file_exists(NV_ROOTDIR . '/themes/' . $global_config['site_theme'] . '/css/' . $mod_file . '.css')) {
                $block_css = $global_config['site_theme'];
            } else {
                $block_css = 'default';
            }

            $xtpl->assign('BLOCK_CSS', $block_css);
            $xtpl->parse('main.css');

            if (file_exists(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/js/' . $mod_file . '.js')) {
                $block_js = $global_config['module_theme'];
            } elseif (file_exists(NV_ROOTDIR . '/themes/' . $global_config['site_theme'] . '/js/' . $mod_file . '.js')) {
                $block_js = $global_config['site_theme'];
            } else {
                $block_js = 'default';
            }

            $xtpl->assign('BLOCK_JS', $block_js);
            $xtpl->parse('main.js');
        }

        $skey = $nv_Request->get_title('q', 'get,post', '');
        $skey2 = $nv_Request->get_title('q2', 'get,post', '');
        // $plan_key2 = $nv_Request->get_title('k2', 'get,post', '');
        $field = $nv_Request->get_array('field', 'get,post', array());
        $bidfieid = $nv_Request->get_typed_array('bidfieid', 'post,get', 'string', array());

        $field_kqmt = $nv_Request->get_array('field_kqmt', 'get,post', array());
        $field_kqcgtt = $nv_Request->get_array('field_kqcgtt', 'get,post', array());
        $phuongthuc = $nv_Request->get_array('phuong_thuc', 'get,post', array());
        // var_dump($phuongthuc);
        $from = nv_date('d/m/Y', NV_CURRENTTIME - (13 * 86400)); // Mặc định 14 ngày gầy đây
        $to = nv_date('d/m/Y', NV_CURRENTTIME);
        $sfrom = nv_substr($nv_Request->get_title('sfrom', 'get', $from), 0, 10);
        $sto = nv_substr($nv_Request->get_title('sto', 'get', $to), 0, 10);
        if (!preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sfrom)) {
            $sfrom = $from;
        }
        if (!preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sto)) {
            $sto = $to;
        }
        $cat = $nv_Request->get_int('cat', 'get', 0);
        $goods_search = $nv_Request->get_int('goods', 'get', 0);
        $goods_search_2 = $nv_Request->get_int('goods_2', 'post,get', 0);
        $rq_form_value = $nv_Request->get_int('rq_form_value', 'post,get', 0);
        $kind = $nv_Request->get_int('searchkind', 'get', 0);
        $type = $nv_Request->get_int('type', 'get', 0) == 1 ? 2 : 1;
        $type_search = $nv_Request->get_int('type_search', 'get', $type);
        $type_info = $nv_Request->get_int('type_info', 'get', 0);
        $type_info2 = $nv_Request->get_int('type_info2', 'get', 0);
        $type_info3 = $nv_Request->get_int('type_info3', 'get', 0);
        $kqlc_tc_dgts = $nv_Request->get_int('ketqua_luachon_tochuc_dgts', 'post,get', 0);
        $type_view_open = $nv_Request->get_int('type_view_open', 'get', 0);
        $sl_nhathau = $nv_Request->get_int('sl_nhathau', 'get', 0);
        $sl_nhathau_cgtt = $nv_Request->get_int('sl_nhathau_cgtt', 'get', 0);
        $without_key = $nv_Request->get_title('without_key', 'post,get'); // Từ khóa loại trừ
        $open_only = $nv_Request->get_int('open_only', 'post,get', 0);
        // $plan_without_key = $nv_Request->get_title('without_key1', 'post,get'); // Từ khóa loại trừ
        $_phanmucid = $nv_Request->get_array('phanmucid', 'get,post', []); // phân mục
        $vsic_code = $nv_Request->get_array('vsic', 'get,post', []); // vsic
        $_lct = $nv_Request->get_typed_array('idlct', 'get,post', 'int', []); // Loại công trình

        $_idprovince = $nv_Request->get_array('idprovince', 'get,post', []); // Tỉnh thành
        $_idprovince_kq = $nv_Request->get_array('idprovincekq', 'get,post', []); // Tỉnh thành
        $_idprovince_kh = $nv_Request->get_array('idprovince_plan', 'get,post', []);
        $_idprovince_khtt = $nv_Request->get_typed_array('idprovince_khtt', 'get,post', 'int', []); // Tỉnh thành của khttlcnt

        $rq_investor = $nv_Request->get_typed_array('rq_investor', 'post,get', 'string', []);
        $rq_province = $nv_Request->get_int('rq_province', 'post,get', 0);
        $rq_district = $nv_Request->get_int('rq_district', 'post,get', 0);
        $rq_ward = $nv_Request->get_int('rq_ward', 'post,get', 0);

        if (!empty($array_op[0])) {
            if ($array_op[0] == 'tinh-thanh' || $array_op[0] == 'tenderlistbylocation') {
                if ($array_op[1] == 'Chua-phan-loai' || $array_op[1] == 'unknown') {
                    $_idprovince[] = -1;
                } else if ($array_op[1] == change_alias($nv_Lang->getModule('alias_vn_out_territory'))) {
                    $_idprovince[] = 825;
                } else if ($array_op[1] == change_alias($nv_Lang->getModule('alias_nationwide'))) {
                    $_idprovince[] = 824;
                } else {
                    $_temp = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE alias = ' . $db->quote($array_op[1]))
                        ->fetchColumn();
                    if (!empty($_temp)) {
                        $_idprovince[] = $_temp;
                    }
                }
            } else if ((empty($_phanmucid) || empty($vsic_code)) && !empty($array_op[1]) && !in_array($array_op[1], [
                    Url::getTBMT(),
                    Url::getNT(),
                    Url::getKQLCNT(),
                    Url::getKHLCNT(),
                    change_alias(strtolower($nv_Lang->getModule('online_bid'))),
                    change_alias(strtolower($nv_Lang->getModule('offline_bid'))),
                    change_alias(strtolower($nv_Lang->getModule('small_org_bid')))
                ])) {
                $last_op_1 = explode('-', $array_op[1]);
                $last_op_1 = end($last_op_1);
                if (preg_match('/^[A-U][0-9]{0,5}$/', $last_op_1)) {
                    $vsic = $db->query('SELECT code FROM ' . NV_PREFIXLANG . '_industry WHERE status = 1 AND code = ' . $db->quote($last_op_1))
                        ->fetchColumn();
                    !empty($vsic) && $vsic_code[] = $vsic;
                } else {
                    $_phanmuc = $db->query('SELECT id FROM ' . BID_PREFIX_GLOBAL . '_phanmuc WHERE parent_id != 0 AND alias_' . NV_LANG_DATA . ' = ' . $db->quote($array_op[1]))
                        ->fetchColumn();
                    !empty($_phanmuc) && $_phanmucid[] = $_phanmuc;
                }
            }
        }
        if (!empty($array_op[3])) {
            if ($array_op[2] == 'tinh-thanh' || $array_op[2] == 'tenderlistbylocation') {
                if ($array_op[3] == 'Chua-phan-loai' || $array_op[3] == 'unknown') {
                    $_idprovince[] = -1;
                } else if ($array_op[3] == change_alias($nv_Lang->getModule('alias_vn_out_territory'))) {
                    $_idprovince[] = 825;
                } else if ($array_op[3] == change_alias($nv_Lang->getModule('alias_nationwide'))) {
                    $_idprovince[] = 824;
                } else {
                    $_temp = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE alias = ' . $db->quote($array_op[3]))
                        ->fetchColumn();
                    if (!empty($_temp)) {
                        $_idprovince[] = $_temp;
                    }
                }
                $_idprovince_kh = $_idprovince_khtt = $_idprovince;
            } else if (empty($_phanmucid) && !empty($array_op[3]) && !in_array($array_op[3], [
                    Url::getTBMT(),
                    Url::getNT(),
                    Url::getKQLCNT(),
                    Url::getKHLCNT(),
                    change_alias(strtolower($nv_Lang->getModule('online_bid'))),
                    change_alias(strtolower($nv_Lang->getModule('offline_bid'))),
                    change_alias(strtolower($nv_Lang->getModule('small_org_bid')))
                ])) {
                $last_op_1 = explode('-', $array_op[3]);
                $last_op_1 = end($last_op_1);
                if (preg_match('/^[A-U][0-9]{0,5}$/', $last_op_1)) {
                    $vsic = $db->query('SELECT code FROM ' . NV_PREFIXLANG . '_industry WHERE status = 1 AND code = ' . $db->quote($last_op_1))
                        ->fetchColumn();
                    !empty($vsic) && $vsic_code[] = $vsic;
                } else {
                    $_phanmuc = $db->query('SELECT id FROM ' . BID_PREFIX_GLOBAL . '_phanmuc WHERE parent_id != 0 AND alias_' . NV_LANG_DATA . ' = ' . $db->quote($array_op[3]))
                        ->fetchColumn();
                    !empty($_phanmuc) && $_phanmucid[] = $_phanmuc;
                }
            }
        }
        $default_advance = 0;

        $type_org = $nv_Request->get_int('type_org', 'get', 1); // Tất cả các doanh nghiệp hay ưu tiên doanh nghiệp nhỏ
        if (!empty($array_op[2])) {
            switch ($array_op[2]) {
                case change_alias(strtolower($nv_Lang->getModule('online_bid'))):
                    $cat = 1;
                    break;
                case change_alias(strtolower($nv_Lang->getModule('offline_bid'))):
                    $cat = 2;
                    break;
                case change_alias(strtolower($nv_Lang->getModule('small_org_bid'))):
                    $type_org = 2;
                    break;
            }
        }
        $money_from = $nv_Request->get_string('money_from', 'get', ''); // Tiền đảm bảo
        $money_from = floatval(str_replace(',', '', $money_from));
        if ($money_from < 0) {
            $money_from_format = '';
        } else if ($money_from > $max_vaule_price) {
            $money_from_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $money_from_format = number_format($money_from, 0, '.', ',');
        }

        $money_to = $nv_Request->get_string('money_to', 'get', ''); // Tiền đảm bảo
        $money_to = floatval(str_replace(',', '', $money_to));
        if ($money_to < 0) {
            $money_to_format = '';
        } else if ($money_to > $max_vaule_price) {
            $money_to_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $money_to_format = number_format($money_to, 0, '.', ',');
        }

        $price_from = $nv_Request->get_string('price_from', 'get', ''); // Giá mời thầu
        $price_from = floatval(str_replace(',', '', $price_from));
        if ($price_from < 0) {
            $price_from_format = '';
        } else if ($price_from > $max_vaule_price) {
            $price_from_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $price_from_format = number_format($price_from, 0, '.', ',');
        }
        $price_to = $nv_Request->get_string('price_to', 'get', '');
        $price_to = floatval(str_replace(',', '', $price_to));
        if ($price_to < 0) {
            $price_to_format = '';
        } else if ($price_to > $max_vaule_price) {
            $price_to_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $price_to_format = number_format($price_to, 0, '.', ',');
        }

        $invest_from = $nv_Request->get_string('invest_from', 'get', ''); // Tổng mức đầu tư
        $invest_from = floatval(str_replace(',', '', $invest_from));
        if ($invest_from < 0) {
            $invest_from_format = '';
        } else if ($invest_from > $max_vaule_price) {
            $invest_from_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $invest_from_format = number_format($invest_from, 0, '.', ',');
        }
        $invest_to = $nv_Request->get_string('invest_to', 'get', ''); // Tổng mức đầu tư
        $invest_to = floatval(str_replace(',', '', $invest_to));
        if ($invest_to < 0) {
            $invest_to_format = '';
        } else if ($invest_to > $max_vaule_price) {
            $invest_to_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $invest_to_format = number_format($invest_to, 0, '.', ',');
        }

        $price_plan_from = $nv_Request->get_string('price_plan_from', 'get', ''); // Giá gói thầu trong KHLCNT
        $price_plan_from = floatval(str_replace(',', '', $price_plan_from));
        if ($price_plan_from < 0) {
            $price_plan_from_format = '';
        } else if ($price_plan_from > $max_vaule_price) {
            $price_plan_from_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $price_plan_from_format = number_format($price_plan_from, 0, '.', ',');
        }

        $price_plan_to = $nv_Request->get_string('price_plan_to', 'get', '');
        $price_plan_to = floatval(str_replace(',', '', $price_plan_to));
        if ($price_plan_to < 0) {
            $price_plan_to_format = '';
        } else if ($price_plan_to > $max_vaule_price) {
            $price_plan_to_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $price_plan_to_format = number_format($price_plan_to, 0, '.', ',');
        }

        $catressult = $nv_Request->get_int('catressult', 'get', 0);
        $win_price_from = $nv_Request->get_string('win_price_from', 'get', ''); // Giá gói thầu trong KHLCNT
        $win_price_from = floatval(str_replace(',', '', $win_price_from));
        if ($win_price_from < 0) {
            $win_price_from_format = '';
        } else if ($win_price_from > $max_vaule_price) {
            $win_price_from_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $win_price_from_format = number_format($win_price_from, 0, '.', ',');
        }

        $win_price_to = $nv_Request->get_string('win_price_to', 'get', '');
        $win_price_to = floatval(str_replace(',', '', $win_price_to));
        if ($win_price_to < 0) {
            $win_price_to_format = '';
        } else if ($win_price_to > $max_vaule_price) {
            $win_price_to_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $win_price_to_format = number_format($win_price_to, 0, '.', ',');
        }
        $is_advance = $nv_Request->get_int('is_advance', 'get', $default_advance);
        $search_type_content = $nv_Request->get_int('search_type_content', 'get', 0);
        $par_search = $nv_Request->get_int('par_search', 'get', 0);
        $par_search != 0 && $goods_search = 0;
        $keyword_min_bid_prices = $nv_Request->get_string('keyword_min_bid_prices', 'get', '');
        $keyword_min_bid_prices = floatval(str_replace(',', '', $keyword_min_bid_prices));
        if ($keyword_min_bid_prices < 0) {
            $keyword_min_bid_prices_format = '';
        } else if ($keyword_min_bid_prices > $max_vaule_price) {
            $keyword_min_bid_prices_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $keyword_min_bid_prices_format = number_format($keyword_min_bid_prices, 0, '.', ',');
        }
        // Nếu giá trị vượt quá 13 số thì chỉ lấy lấy 13 số
        if ($type_search == 3 && strlen($keyword_min_bid_prices) > 13) {
            $keyword_min_bid_prices = substr($keyword_min_bid_prices, 0, 12);
        }
        $keyword_max_bid_prices = $nv_Request->get_string('keyword_max_bid_prices', 'get', '');
        $keyword_max_bid_prices = floatval(str_replace(',', '', $keyword_max_bid_prices));
        if ($keyword_max_bid_prices < 0) {
            $keyword_max_bid_prices_format = '';
        } else if ($keyword_max_bid_prices > $max_vaule_price) {
            $keyword_max_bid_prices_format = number_format($max_vaule_price, 0, '.', ',');
        } else {
            $keyword_max_bid_prices_format = number_format($keyword_max_bid_prices, 0, '.', ',');
        }

        // Nếu giá trị vượt quá 13 số thì chỉ lấy lấy 13 số
        if ($type_search == 3 && strlen($keyword_max_bid_prices) > 13) {
            $keyword_max_bid_prices = substr($keyword_max_bid_prices, 0, 12);
        }

        $keyword_id_bidder = $nv_Request->get_int('keyword_id_bidder', 'get', 0);
        $sprovince = $nv_Request->get_int('keyword_id_province', 'get', 0);
        $sregion = $nv_Request->get_int('idregion', 'get', 0);
        $sregion_plan = $nv_Request->get_int('idregion_plan', 'get', 0);
        $sdistrict = $nv_Request->get_int('keyword_id_district', 'get', 0);
        $oda = $nv_Request->get_int('oda', 'get', 0);
        $khlcnt = $nv_Request->get_int('khlcnt', 'get', 0);
        $type_bid = $nv_Request->get_int('type_bid', 'get', 0);
        $type_choose_id = $nv_Request->get_int('type_choose_id', 'get', 0);
        $search_one_key = $nv_Request->get_int('search_one_key', 'get', 0);
        $type_kqlcnt = $nv_Request->get_int('type_kqlcnt', 'get', 0); // Trạng thái liên kết với KHLCNT
        if (!empty($array_op[1]) and (!empty($array_op[2]) and ($array_op[2] == 'linked' or $array_op[2] == 'unlinked'))) {
            switch ($array_op[2]) {
                case 'linked':
                    $type_kqlcnt = 1;
                    break;
                case 'unlinked':
                    $type_kqlcnt = 2;
                    break;
            }
        }

        // Tự xác định loại tìm kiếm khi vào khu vực tương ứng của module
        if (empty($type_info)) {
            if ($module == $module_name and isset($module_info['funcs'][$op])) {
                if ($module_info['funcs'][$op]['func_name'] == 'projectdetail' || $module_info['funcs'][$op]['func_name'] == 'project' || $module_info['funcs'][$op]['func_name'] == 'listtbkssqt') {
                    $type_search = 2;
                }
                if (isset($array_op[1])) {
                    if ($array_op[1] == Url::getTBMT() or $array_op[1] == Url::getNT() or $array_op[1] == Url::getNT1() or $array_op[1] == Url::getKHLCNT() or $array_op[1] == Url::getKQLCNT() or $array_op[0] == $module_info['alias']['planoverall'] or $array_op[0] == $module_info['alias']['phan-muc'] or $array_op[0] == $module_info['alias']['vsic-industry'] or $array_op[0] == $module_info['alias']['viewopen'] or $array_op[0] == $module_info['alias']['tinh-thanh'] || $array_op[1] == change_alias(strtolower($nv_Lang->getModule('online_bid'))) || $array_op[1] == change_alias(strtolower($nv_Lang->getModule('offline_bid'))) || $array_op[1] == change_alias(strtolower($nv_Lang->getModule('small_org_bid'))) || $array_op[0] == $module_info['alias']['request-quote'] || $array_op[0] == $module_info['alias']['bidding-contract'] || $array_op[1] == Url::getKQLCNT() . '/linked' || $array_op[1] == Url::getKQLCNT() . '/unlinked' or ($array_op[1] == Url::getKQLCNT() && !empty($array_op[2]) && ($array_op[2] == 'linked' || $array_op[2] == 'unlinked')) || $array_op[0] == $module_info['alias']['reoffer'] || $array_op[0] == $module_info['alias']['reoffer-room']) {
                        $type_search = 1;
                        if (!empty($cat) || $type_org == 2) {
                            $is_advance = 1;
                        }
                    } else {
                        $type_search = 2;
                    }
                }

                if ($type_search == 2) {
                    if ($module_info['funcs'][$op]['func_name'] == 'view' || $module_info['funcs'][$op]['func_name'] == 'detail' || $module_info['funcs'][$op]['func_name'] == 'thongbao') {
                        $type_info2 = 2;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'plans' || $module_info['funcs'][$op]['func_name'] == 'listplan' || $module_info['funcs'][$op]['func_name'] == 'kehoach') {
                        $type_info2 = 4;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'listresult' || $module_info['funcs'][$op]['func_name'] == 'result' || $module_info['funcs'][$op]['func_name'] == 'ketqua') {
                        $type_info2 = 5;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'listprequalification' || $module_info['funcs'][$op]['func_name'] == 'prequalification' || $module_info['funcs'][$op]['func_name'] == 'moisotuyen') {
                        $type_info2 = 3;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'listresultpq' || $module_info['funcs'][$op]['func_name'] == 'resultpq' || $module_info['funcs'][$op]['func_name'] == 'ketquasotuyen') {
                        $type_info2 = 6;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'listtbkssqt' || $module_info['funcs'][$op]['func_name'] == 'tbkssqt') {
                        $type_info2 = 8;
                    } else {
                        $type_info2 = 1;
                    }
                } else {
                    if ($module_info['funcs'][$op]['func_name'] == 'plans' || $module_info['funcs'][$op]['func_name'] == 'listplan' || $module_info['funcs'][$op]['func_name'] == 'thongbao') {
                        $type_info = 2;
                    } elseif (isset($array_op[0]) and $array_op[0] == Url::getMQTNT()) {
                        $type_info = 12;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'listprequalification' || $module_info['funcs'][$op]['func_name'] == 'prequalification' || $module_info['funcs'][$op]['func_name'] == 'moisotuyen') {
                        $type_info = 4;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'result' || $module_info['funcs'][$op]['func_name'] == 'listresult' || $module_info['funcs'][$op]['func_name'] == 'ketqua') {
                        $type_info = 3;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'open' || $module_info['funcs'][$op]['func_name'] == 'viewopen' || $module_info['funcs'][$op]['func_name'] == 'ketquamothau') {
                        $type_info = 5;
                    } elseif (isset($array_op[0]) and $array_op[0] == Url::getKQMQTNT()) {
                        $type_info = 13;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'listresultpq' || $module_info['funcs'][$op]['func_name'] == 'resultpq' || $module_info['funcs'][$op]['func_name'] == 'ketquasotuyen') {
                        $type_info = 6;
                    } elseif (isset($array_op[0]) and $array_op[0] == Url::getKQMHSQTNT()) {
                        $type_info = 14;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'listopenpq' || $module_info['funcs'][$op]['func_name'] == 'viewopenpq' || $module_info['funcs'][$op]['func_name'] == 'ketquamosotuyen') {
                        $type_info = 10;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'listplanoverall' || $module_info['funcs'][$op]['func_name'] == 'planoverall' || $module_info['funcs'][$op]['func_name'] == 'kehoachtongthe') {
                        $type_info = 15;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'request-quote' || $module_info['funcs'][$op]['func_name'] == 'request-quote-detail') {
                        $type_info = 17;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'bidding-contract' || $module_info['funcs'][$op]['func_name'] == 'bidding-contract-detail') {
                        $type_info = 22;
                    } elseif ($module_info['funcs'][$op]['func_name'] == 'reoffer' || $module_info['funcs'][$op]['func_name'] == 'reoffer-list' || $module_info['funcs'][$op]['func_name'] == 'reoffer-room') {
                        $type_info = 42;
                    } else {
                        $type_info = 1;
                    }
                }
            } else if ($module_name == 'dau-gia') {
                $type_search = 3;
                $type_info3 = 1;
                if ($site_mods[$module_name]['funcs'][$op]['func_name'] == 'bidorganization' || $site_mods[$module_name]['funcs'][$op]['func_name'] == 'bidorganizationdetail') {
                    $type_info3 = 2;
                }
            } else if ($module_name == 'devprojects' or $module_name == 'project-investment') {
                $type_search = 1;
                $type_info = 7;
            } else {
                // Mặc định tìm thông báo mời thầu
                $type_info = 1;
            }
        }

        if ($block_config['style'] == 'center') {
            $time_24h = strtotime("-1 day");
            // $time_1w = strtotime("-1 week");
            $time_1m = strtotime("-1 month");
            $time_1y = strtotime("-1 year");

            $cache_file = 'nv4_vi_' . $module . '_stat_' . NV_CACHE_PREFIX . '.cache';
            // dùng chung nv4_vi cho thống kê 2 ngôn ngữ cùng thông số

            if (($cache = $nv_Cache->getItem($module, $cache_file, 1800)) != false) {
                $results = unserialize($cache);
            } else {
                $results = [];
                $db->sqlreset()
                    ->select('COUNT(IF(den_ngay > ' . NV_CURRENTTIME . ', 1, NULL)) as tbmt_mo, COUNT(IF(ngay_dang_tai > ' . $time_24h . ', 1, NULL)) as tbmt_24h, COUNT(IF(ngay_dang_tai > ' . $time_1m . ', 1, NULL)) as tbmt_1m')
                    ->from('nv4_vi_' . $module_data . '_row')
                    ->where('ngay_dang_tai > ' . $time_1m);
                $sth = $db->prepare($db->sql());
                $sth->execute();
                $results += $sth->fetch();

                $db->select('COUNT(IF(addtime > ' . $time_24h . ', 1, NULL)) as plans_24h, COUNT(IF(addtime > ' . $time_1m . ', 1, NULL)) as plans_1m')
                    ->from('nv4_vi_' . $module_data . '_plans')
                    ->where('addtime > ' . $time_1m);
                $sth = $db->prepare($db->sql());
                $sth->execute();
                $results += $sth->fetch();

                // đọc từ bảng thống kê theo ngày số lượng của năm, bảng này chỉ có dữ liệu ngày hôm trc, chạy vào 23h59
                $db->select('SUM(bid) as tbmt_1y')
                    ->from(BID_PREFIX_GLOBAL . '_daily_statistic')
                    ->where('date >= ' . $time_1y . ' AND date <=' . NV_CURRENTTIME);
                $sth = $db->prepare($db->sql());
                $sth->execute();
                $results += $sth->fetch();

                // cộng thêm dữ liệu ngày hiện tại, tính từ 00h
                $db->sqlreset()
                    ->select('COUNT(*) as current_day')
                    ->from('nv4_vi_' . $module_data . '_row')
                    ->where('ngay_dang_tai >= ' . mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME)));
                $sth = $db->prepare($db->sql());
                $sth->execute();
                $results_day = $sth->fetch();
                if (!empty($results_day)) {
                    $results['tbmt_1y'] += $results_day['current_day'];
                }

                $results_cache = serialize($results);
                $nv_Cache->setItem($module, $cache_file, $results_cache);
            }

            foreach ($results as $key => $value) {
                $results[$key] = nv_number_format($value);
            }
        }

        if (!$global_config['rewrite_enable']) {
            if ($type_search == 2) {
                $_module = $block_config['module'];
                $op_variable = 'project';
                if ($type_info2 == 2)
                    $op_variable = 'detail';
                elseif ($type_info2 == 3)
                    $op_variable = 'listprequalification';
                elseif ($type_info2 == 4)
                    $op_variable = 'listplan';
                elseif ($type_info2 == 5)
                    $op_variable = 'listresult';
                elseif ($type_info2 == 6)
                    $op_variable = 'listresultpq';
                elseif ($type_info2 == 8)
                    $op_variable = 'listtbkssqt';
            } else if ($type_search == 3) {
                $_module = 'dau-gia';
                if ($type_info3 == 1) {
                    $op_variable = 'main';
                } else {
                    $op_variable = 'bidorganization';
                }
            } else {
                $_module = $block_config['module'];
                $op_variable = 'listresult';
                if ($type_info == 1)
                    $op_variable = 'detail';
                elseif ($type_info == 2)
                    $op_variable = 'listplan';
                elseif ($type_info == 5)
                    $op_variable = 'open';
                elseif ($type_info == 4 || $type_info == 12)
                    $op_variable = 'listprequalification';
                elseif ($type_info == 6 || $type_info == 13)
                    $op_variable = 'listresultpq';
                elseif ($type_info == 7)
                    $_module = 'devprojects';
                elseif ($type_info == 10 || $type_info == 14)
                    $form_action = 'listopenpq';
                elseif ($type_info == 15)
                    $form_action = 'listplanoverall';
                elseif ($type_info == 17)
                    $form_action = 'request-quote';
                elseif ($type_info == 17)
                    $form_action = 'bidding-contract';
                elseif ($type_info == 42)
                    $form_action = $module_info['alias']['reoffer-list'];
                $op_variable = 'main';
            }

            $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
            $xtpl->assign('NV_OP_VARIABLE', $op_variable);
            $xtpl->assign('MODULE_NAME', $_module);
            $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
            $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
            $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . "index.php");
            $xtpl->parse('main.no_rewrite');
        } else {
            $_module = $block_config['module'];
            if ($type_search == 2) {
                $form_action = 'project';
                if ($type_info2 == 2)
                    $form_action = 'detail';
                elseif ($type_info2 == 3)
                    $form_action = 'listprequalification';
                elseif ($type_info2 == 4)
                    $form_action = 'listplan';
                elseif ($type_info2 == 5)
                    $form_action = 'listresult';
                elseif ($type_info2 == 6)
                    $form_action = 'listresultpq';
                elseif ($type_info2 == 8)
                    $form_action = 'listtbkssqt';
            } else if ($type_search == 3) {
                if ($type_info3 == 1) {
                    $form_action = 'main';
                } else {
                    $form_action = 'bidorganization';
                }

                $_module = 'dau-gia';
            } else {
                $form_action = $tbmt_alias;
                if ($type_info == 2)
                    $form_action = 'listplan';
                elseif ($type_info == 3)
                    $form_action = 'listresult';
                elseif ($type_info == 4 || $type_info == 12)
                    $form_action = 'listprequalification';
                elseif ($type_info == 5)
                    $form_action = 'open';
                elseif ($type_info == 6 || $type_info == 13)
                    $form_action = 'listresultpq';
                elseif ($type_info == 10 || $type_info == 14)
                    $form_action = 'listopenpq';
                elseif ($type_info == 15)
                    $form_action = 'listplanoverall';
                elseif ($type_info == 17)
                    $form_action = 'request-quote';
                elseif ($type_info == 22)
                    $form_action = 'bidding-contract';
                elseif ($type_info == 42)
                    $form_action = $module_info['alias']['reoffer-list'];
                elseif ($type_info == 7) {
                    $_module = 'devprojects';
                    $form_action = 'main';
                }
            }
            if (isset($array_op[1])) {
                if ($array_op[1] == change_alias(strtolower($nv_Lang->getModule('online_bid'))) || $array_op[1] == change_alias(strtolower($nv_Lang->getModule('offline_bid'))) || $array_op[1] == change_alias(strtolower($nv_Lang->getModule('small_org_bid')))) {
                    $form_action2 = $array_op[1];
                }
            }
            $is_kqlcnt = 0;
            if (isset($array_op[2]) && $type_info == 3) {
                $is_kqlcnt = 1;
                if ($array_op[2] == 'linked' || $array_op[2] == 'unlinked') {
                    $form_action2 = $array_op[2];
                }
            }
            $xtpl->assign('ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module . '&amp;' . NV_OP_VARIABLE . '=' . $form_action . (isset($form_action2) ? '/' . $form_action2 : ''), true));
            $is_province = 0;
            if (!empty($array_op[3]) && $type_info == 1) {
                $is_province = 1;
                if ($array_op[2] == 'tinh-thanh' || $array_op[2] == 'tenderlistbylocation') {
                    $url_suffix = NV_LANG_DATA == 'vi' ? 'tinh-thanh' : 'tenderlistbylocation';
                    $xtpl->assign('ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . Url::getTBMT() . '/' . $url_suffix . '/' . $array_op[3], true));
                }
            }
            // Đảm bảo form action mặc định cho tìm kiếm tỉnh thành
            if ($type_info == 1 && $type_search == 1 && empty($array_op[3])) {
                $xtpl->assign('ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . Url::getTBMT(), true));
            }
            $xtpl->assign('FORM_ACTION', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . Url::getTBMT(), true));
            $xtpl->assign('FORM_ACTION1', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['plans'] . '/' . Url::getKHLCNT(), true));
            $xtpl->assign('FORM_ACTION2', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT(), true));
            $xtpl->assign('FORM_ACTION4', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['resultpq'] . '/' . Url::getNT(), true));
            $xtpl->assign('FORM_ACTION3', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['prequalification'] . '/' . Url::getNT(), true));
            $xtpl->assign('FORM_ACTION5', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=open', true));
            $xtpl->assign('FORM_ACTION6', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=project', true));
            $xtpl->assign('FORM_ACTION10', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['viewopenpq'] . '/' . Url::getNT(), true));
            $xtpl->assign('FORM_ACTION11', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $tbmt_alias, true));
            $xtpl->assign('FORM_ACTION15', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['planoverall'] . '/' . Url::getKHTTLCNT(), true));

            $xtpl->assign('FORM_ACTION7', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia&amp;' . NV_OP_VARIABLE . '=main', true));
            $xtpl->assign('FORM_ACTION8', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia&amp;' . NV_OP_VARIABLE . '=bidorganization', true));

            $xtpl->assign('FORM_ACTION9', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=devprojects&amp;' . NV_OP_VARIABLE . '=main', true));
            $xtpl->assign('FORM_ACTION16', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $tbmt_alias_t, true));

            $xtpl->assign('FORM_ACTION17', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=request-quote', true));
            $xtpl->assign('FORM_ACTION18', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT() . '/linked', true));
            $xtpl->assign('FORM_ACTION19', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT() . '/unlinked', true));
            $xtpl->assign('FORM_ACTION20', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT(), true));
            $xtpl->assign('FORM_ACTION21', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=listtbkssqt', true));
            $xtpl->assign('FORM_ACTION22', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=bidding-contract', true));
            $xtpl->assign('FORM_ACTION42', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['reoffer-list'], true));
        }

        $xtpl->assign('LINK_DAUTHAU', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $block_config['module'] . '&amp;' . NV_OP_VARIABLE . '=detail', true));
        $xtpl->assign('LINK_DAUGIA', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia', true));
        $xtpl->assign('is_province', $is_province);
        $xtpl->assign('is_kqlcnt', $is_kqlcnt);
        $xtpl->assign('Q', $skey); // từ khóa chính
        $xtpl->assign('OP', $op);
        // $xtpl->assign('K2', $plan_key2); // từ khóa chính
        $xtpl->assign('Q2', $skey2); // từ khóa bổ sung
        $xtpl->assign('FROM', $sfrom);
        $xtpl->assign('FROM_DEFAULT', $from);
        $xtpl->assign('TO', $sto);
        $xtpl->assign('TO_DEFAULT', $to);
        $xtpl->assign('Q1', $without_key); // Từ khóa loại trừ
        // $xtpl->assign('K1', $plan_without_key); // Từ khóa loại trừ
        $xtpl->assign('MONEY_FROM', $money_from > 0 ? $money_from : '');
        $xtpl->assign('MONEY_FROM_FORMAT', $money_from_format);
        $xtpl->assign('MONEY_TO', $money_to > 0 ? $money_to : '');
        $xtpl->assign('MONEY_TO_FORMAT', $money_to_format);
        $xtpl->assign('PRICE_FROM', $price_from > 0 ? $price_from : '');
        $xtpl->assign('PRICE_FROM_FORMAT', $price_from_format);
        $xtpl->assign('PRICE_TO', $price_to > 0 ? $price_to : '');
        $xtpl->assign('PRICE_TO_FORMAT', $price_to_format);

        $xtpl->assign('INVEST_FROM', $invest_from > 0 ? $invest_from : '');
        $xtpl->assign('INVEST_FROM_FORMAT', $invest_from_format);
        $xtpl->assign('INVEST_TO', $invest_to > 0 ? $invest_to : '');
        $xtpl->assign('INVEST_TO_FORMAT', $invest_to_format);
        $xtpl->assign('PRICE_PLAN_FROM', $price_plan_from > 0 ? $price_plan_from : '');
        $xtpl->assign('PRICE_PLAN_FROM_FORMAT', $price_plan_from_format);
        $xtpl->assign('PRICE_PLAN_TO', $price_plan_to > 0 ? $price_plan_to : '');
        $xtpl->assign('PRICE_PLAN_TO_FORMAT', $price_plan_to_format);
        $xtpl->assign('keyword_min_bid_prices', $keyword_min_bid_prices > 0 ? $keyword_min_bid_prices : '');
        $xtpl->assign('keyword_max_bid_prices', $keyword_max_bid_prices > 0 ? $keyword_max_bid_prices : '');
        $xtpl->assign('keyword_min_bid_prices_format', $keyword_min_bid_prices_format);
        $xtpl->assign('keyword_max_bid_prices_format', $keyword_max_bid_prices_format);

        $xtpl->assign('WIN_PRICE_TO', $win_price_to > 0 ? $win_price_to : '');
        $xtpl->assign('WIN_PRICE_PROM', $win_price_from > 0 ? $win_price_from : '');
        $xtpl->assign('WIN_PRICE_TO_FORMAT', $win_price_to_format);
        $xtpl->assign('WIN_PRICE_FROM_FORMAT', $win_price_from_format);
        $xtpl->assign('YEAR', nv_date('Y', NV_CURRENTTIME) + 1);

        $xtpl->assign('SREGION', $sregion);
        $xtpl->assign('SPROVINCE', $sprovince);
        $xtpl->assign('SDISTRICT', $sdistrict);

        $xtpl->assign('RQ_INVESTOR', $rq_investor);
        $xtpl->assign('RQ_PROVINCE', $rq_province);
        $xtpl->assign('RQ_DISTRICT', $rq_district);
        $xtpl->assign('RQ_WARD', $rq_ward);
        $xtpl->assign('TIMESTAMP', NV_CURRENTTIME);


        $maxspan = defined('NV_IS_ADMIN') ? 24 : $module_config['bidding']['search_mysql_range'];
        $xtpl->assign('MAXSPAN', $maxspan);

        if ($maxspan % 12 == 0) {
            // Chuyển tháng lên năm
            $xtpl->assign('LAST_MAXSPAN_LANG', sprintf($nv_Lang->getModule('last_maxspan_years'), $maxspan / 12));
        } else {
            // Hiển thị tháng
            $xtpl->assign('LAST_MAXSPAN_LANG', sprintf($nv_Lang->getModule('last_maxspan_months'), $maxspan));
        }

        /**
         *
         * <AUTHOR>
         *         Hiển thị menu toàn bộ lịch sử nếu có kích hoạt Elasticsearch
         *         Vì khi tắt Elasticsearch thì chỉ tìm được tối đa xx tháng trong cấu hình
         *         Cái này a Tú thêm
         */
        if (!empty($module_config[$module]['elas_use'])) {
            $xtpl->assign('LAST_ALL_DAYS', $nv_Lang->getModule('last_all_days'));
        } else {
            $xtpl->assign('LAST_ALL_DAYS', '');
        }

        $xtpl->assign('MINDATE', $module_config['bidding']['search_mindate']);

        if ($type_search == 2) {
            $xtpl->assign('TYPE_SEARCH2', ' checked');
        } else if ($type_search == 3) {
            $xtpl->assign('TYPE_SEARCH3', ' checked');
        } else {
            $xtpl->assign('TYPE_SEARCH1', ' checked');
        }

        if ($type_search == 3) {
            $xtpl->parse('main.type_search3');
        } else {
            $xtpl->parse('main.type_search12');
        }

        $xtpl->assign('SEARCH_TYPE_CONTENT', $search_type_content == 1 ? ' checked="checked"' : '');
        $xtpl->assign('SEARCH_ONE_KEY', $search_one_key == 1 ? ' checked="checked"' : '');
        $xtpl->assign('OPEN_ONLY', $open_only == 1 ? ' checked="checked"' : '');
        $xtpl->assign('PAR_SEARCH', $par_search == 1 ? ' checked="checked"' : '');
        $xtpl->assign('LANG_PAR_SEARCH', $type_search == 3 ? $nv_Lang->getModule('par_search2') : $nv_Lang->getModule('par_search'));
        $xtpl->assign('TYPE_ORG_DEFAULT', $cat > 0 ? 1 : $type_org);
        $xtpl->assign('CAT_DEFAULT', $cat);

        $array_type_htdauthau = [
            // 1 => 'Trong nước',
            // 2 => 'Quốc tế',
            // 3 => 'Không sơ tuyển',
            // 4 => 'Sơ tuyển',
            // 5 => 'Không qua mạng',
            // 6 => 'Qua mạng',
            // 7 => 'Một giai đoạn một túi hồ sơ',
            // 8 => 'Một giai đoạn hai túi hồ sơ',
            // 9 => 'Hai giai đoạn một túi hồ sơ',
            // 10 => 'Hai giai đoạn hai túi hồ sơ',
            // 11 => $nv_Lang->getModule('type_lc_11'),
            12 => $nv_Lang->getModule('type_lc_12'),
            // 13 => $nv_Lang->getModule('type_lc_13'),
            14 => $nv_Lang->getModule('type_lc_14'),
            15 => $nv_Lang->getModule('type_lc_15'),
            16 => $nv_Lang->getModule('type_lc_16'),
            17 => $nv_Lang->getModule('type_lc_17'),
            // 18 => 'Lựa chọn theo mục ngân sách cố định',
            19 => $nv_Lang->getModule('type_lc_19'),
            20 => $nv_Lang->getModule('type_lc_20'),
            // 21 => $nv_Lang->getModule('type_lc_21'),
            22 => $nv_Lang->getModule('type_lc_22'),
            23 => $nv_Lang->getModule('type_lc_23'),
            42 => $nv_Lang->getModule('type_lc_42')
            // 24 => $nv_Lang->getModule('type_lc_24'),
            // 25 => $nv_Lang->getModule('type_lc_25'),
            // 26 => 'Tuyển chọn trên cơ sở chất lượng',
            // 27 => 'Tuyển chọn từ một nguồn duy nhất',
            // 28 => $nv_Lang->getModule('type_lc_28'),
            // 29 => 'Tuyển chọn tư vấn có chi phí thấp',
        ];

        $arr_type_kqlcnt = [
            0 => $nv_Lang->getModule('all_kqlcnt'),
            1 => $nv_Lang->getModule('type_kqlcnt_link_khlcnt'),
            2 => $nv_Lang->getModule('type_kqlcnt_no_link_khlcnt')
        ];

        $arr_cat = array(
            1 => $nv_Lang->getModule('cat_filters_1'),
            2 => $nv_Lang->getModule('cat_filters_2'),
            0 => $nv_Lang->getModule('all')
        );

        $arr_cat_action = [
            1 => change_alias(strtolower($nv_Lang->getModule('online_bid'))),
            2 => change_alias(strtolower($nv_Lang->getModule('offline_bid'))),
            0 => ''
        ];

        $arr_goods_search = [
            0 => $nv_Lang->getModule('goods_search_0'),
            1 => $nv_Lang->getModule('goods_search_1'),
            2 => $nv_Lang->getModule('goods_search_2')
        ];

        // hàng hóa/dịch vụ ycbg
        $arr_form_value_search = [
            0 => $nv_Lang->getModule('rq_form_value_search_0'),
            1 => $nv_Lang->getModule('rq_form_value_search_1'),
            2 => $nv_Lang->getModule('rq_form_value_search_2')
        ];

        $arr_kind = array(
            1 => $nv_Lang->getModule('kind_filters_1'),
            2 => $nv_Lang->getModule('kind_filters_2'),
            0 => $nv_Lang->getModule('kind_filters_0')
        );

        $arr_info = array(
            7 => $nv_Lang->getModule('dev_project'),
            15 => $nv_Lang->getModule('pagetitle_khttlcnt'),
            2 => $nv_Lang->getModule('plan_tl'),
            4 => $nv_Lang->getModule('prequalification_title'),
            12 => $nv_Lang->getModule('prequalification_qt'),
            6 => $nv_Lang->getModule('result_prequalification'),
            13 => $nv_Lang->getModule('result_prequalification_title_qt'),
            1 => $nv_Lang->getModule('pagetitle_tbmt_type1'),
            5 => $nv_Lang->getModule('listopen'),
            42 => $nv_Lang->getModule('listreoffer'),
            3 => $nv_Lang->getModule('kq_lcnt'),
            10 => $nv_Lang->getModule('rs_open_prequalification'),
            14 => $nv_Lang->getModule('rs_open_prequalification_qt'),
            17 => $nv_Lang->getModule('ycbg')
        );
        if (defined('NV_IS_ADMIN')) {
            $arr_info[22] = $nv_Lang->getModule('hddt');
        }

        $arr_info2 = array(
            1 => $nv_Lang->getModule('project_proposal'),
            8 => $nv_Lang->getModule('listtbkssqt'),
            4 => $nv_Lang->getModule('ke_hoach_lcndt'),
            3 => $nv_Lang->getModule('info_filters_3'),
            6 => $nv_Lang->getModule('result_prequalification_project'),
            2 => $nv_Lang->getModule('pagetitle_tbmt_type2'),
            5 => $nv_Lang->getModule('ket_qua_lcndt')
            // 7 => 'Hợp đồng'
        );
        $arr_info3 = array(
            1 => $nv_Lang->getModule('info_filters_7'),
            2 => $nv_Lang->getModule('info_filters_8')
        );

        $arr_field = array(
            1 => $nv_Lang->getModule('field_1'),
            2 => $nv_Lang->getModule('field_2'),
            3 => $nv_Lang->getModule('field_3'),
            4 => $nv_Lang->getModule('field_4'),
            5 => $nv_Lang->getModule('field_5'),
            // 6 => 'Dự án đầu tư'
            7 => $nv_Lang->getModule('field_7'),
            8 => $nv_Lang->getModule('field_8'),
            9 => $nv_Lang->getModule('field_9'),
            10 => $nv_Lang->getModule('field_10')
        );

        $arr_bidfieid = [];
        $arr_bidfieid['HH'] = $nv_Lang->getModule('field_1');
        $arr_bidfieid['XL'] = $nv_Lang->getModule('field_2');
        $arr_bidfieid['PTV'] = $nv_Lang->getModule('field_4');
        $arr_bidfieid['TV'] = $nv_Lang->getModule('field_3');
        $arr_bidfieid['HON_HOP'] = $nv_Lang->getModule('field_5');
        $arr_bidfieid['Other'] = $nv_Lang->getModule('custom_range');

        $arr_org = array(
            1 => $nv_Lang->getModule('all'),
            2 => $nv_Lang->getModule('type_bussniess')
        );
        $arr_org_action = array(
            1 => '',
            2 => change_alias(strtolower($nv_Lang->getModule('small_org_bid')))
        );

        if ($block_config['style'] == 'center') {
            $ls = [
                'tbmt_mo',
                'tbmt_24h',
                'plans_24h',
                'tbmt_1m',
                'plans_1m',
                'tbmt_1y'
            ];
            $date_from_24h = nv_date('d/m/Y', NV_CURRENTTIME - 86400);
            $date_from_1m = nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 30);
            $date_from_1y = nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 365);
            $date_to = nv_date('d/m/Y', NV_CURRENTTIME);
            foreach ($ls as $k) {
                if (!empty($results[$k])) {
                    $tbmt_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . '/' . Url::getTBMT();
                    $plans_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['listplan'];
                    $url = match ($k) {
                        'tbmt_mo' => '#',
                        'tbmt_24h' => $tbmt_url . '&amp;sfrom=' . $date_from_24h . '&amp;sto=' . $date_to,
                        'plans_24h' => $plans_url . '&amp;sfrom=' . $date_from_24h . '&amp;sto=' . $date_to,
                        'tbmt_1m' => $tbmt_url . '&amp;sfrom=' . $date_from_1m . '&amp;sto=' . $date_to,
                        'plans_1m' => $plans_url . '&amp;sfrom=' . $date_from_1y . '&amp;sto=' . $date_to,
                        'tbmt_1y' => $tbmt_url . '&amp;sfrom=' . $date_from_1y . '&amp;sto=' . $date_to
                    };
                    $xtpl->assign('THONGKE', sprintf($nv_Lang->getModule($k), '<strong><a class="text-white" href="' . $url . '">' . $results[$k] . '</a></strong>'));
                    $xtpl->parse('main.thongke');
                }
            }
        }

        foreach ($arr_info as $key => $t) {
            $xtpl->assign('TYPE', array(
                'key' => $key,
                'title' => $t,
                'selected' => $key == $type_info ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.type_info');
        }
        foreach ($arr_info2 as $key => $t) {
            $xtpl->assign('TYPE2', array(
                'key' => $key,
                'title' => $t,
                'selected' => $key == $type_info2 ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.type_info2');
        }
        foreach ($arr_info3 as $key => $t) {
            $xtpl->assign('TYPE3', array(
                'key' => $key,
                'title' => $t,
                'selected' => $key == $type_info3 ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.type_info3');
        }
        foreach ([
                     0 => $nv_Lang->getModule('result_select'),
                     1 => $nv_Lang->getModule('has_result'),
                     -1 => $nv_Lang->getModule('no_result')
                 ] as $key => $t) {
            $xtpl->assign('kqlctc_dgts', array(
                'key' => $key,
                'title' => $t,
                'selected' => $key == $kqlc_tc_dgts ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.kqlctc_dgts');
        }
        for ($i = 0; $i <= 5; $i++) {
            $xtpl->assign('TYPE_OPEN', array(
                'key' => $i,
                'title' => $nv_Lang->getModule('kqmt_status' . $i),
                'selected' => $i == $type_view_open ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.type_view_open');
        }
        for ($i = 0; $i <= 4; $i++) {
            $xtpl->assign('SL_NHATHAU', array(
                'key' => $i,
                'title' => $nv_Lang->getModule('sl_nt_' . $i),
                'selected' => $i == $sl_nhathau ? ' selected="selected"' : '',
                'selected_cgtt' => $i == $sl_nhathau_cgtt ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.sl_nhathau');
            $xtpl->parse('main.sl_nhathau_cgtt');
        }
        if ($type_search == 2) {
            $xtpl->parse('main.type_info_hide');
            $xtpl->parse('main.type_info3_hide');
        } else if ($type_search == 3) {
            $xtpl->parse('main.type_info2_hide');
            $xtpl->parse('main.type_info_hide');
        } else {
            $xtpl->parse('main.type_info2_hide');
            $xtpl->parse('main.type_info3_hide');
        }

        foreach ($arr_org as $key => $org) {
            $xtpl->assign('ORG', array(
                'key' => $key,
                'title' => $org,
                'checked' => $key == $type_org ? ' checked="checked"' : '',
                'action' => ($key > 1 ? nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module . '&amp;' . NV_OP_VARIABLE . '=' . $tbmt_alias . '/' . $arr_org_action[$key], true) : '')
            ));
            $xtpl->parse('main.org');
        }

        foreach ($arr_cat as $key => $c) {
            $xtpl->assign('CAT', array(
                'key' => $key,
                'title' => $c,
                'checked' => $key == $cat ? ' checked' : '',
                'checked_result' => $key == $catressult ? ' checked' : '',
                'action' => ($key > 0 ? nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module . '&amp;' . NV_OP_VARIABLE . '=' . $tbmt_alias . '/' . $arr_cat_action[$key], true) : '')
            ));
            $xtpl->parse('main.catressult');
            $xtpl->parse('main.cat');
        }

        foreach ($arr_type_kqlcnt as $key => $x) {
            $xtpl->assign('TYPE_KQLCNT', array(
                'key' => $key,
                'title' => $x,
                'checked' => $key == $type_kqlcnt ? ' checked' : ''
            ));
            $xtpl->parse('main.type_kqlcnt_link');
        }

        foreach ($arr_goods_search as $key => $c) {
            $xtpl->assign('GOODS', array(
                'key' => $key,
                'title' => $c,
                'checked' => $key == $goods_search ? ' checked' : ''
            ));
            $xtpl->parse('main.goods');
            $xtpl->assign('GOODS2', array(
                'key' => $key,
                'title' => $c,
                'checked' => $key == $goods_search_2 ? ' checked' : ''
            ));
            $xtpl->parse('main.goods_2');
        }

        foreach ($arr_form_value_search as $key => $c) {
            $xtpl->assign('FORM_VALUE', array(
                'key' => $key,
                'title' => $c,
                'checked' => $key == $rq_form_value ? ' checked' : ''
            ));
            $xtpl->parse('main.rq_form_value');
        }

        foreach ($arr_kind as $key => $c) {
            $xtpl->assign('KIND', array(
                'key' => $key,
                'title' => $c,
                'checked' => $key == $kind ? ' checked' : ''
            ));
            $xtpl->parse('main.kind');
        }

        if ($type_search == 2) {
            $hide_keys = [
                1,
                2,
                3,
                4,
                5
            ];
        } else {
            $hide_keys = [
                7,
                8,
                9,
                10
            ];
        }
        foreach ($arr_field as $key => $f) {
            $xtpl->assign('FIELD', array(
                'key' => $key,
                'title' => $f,
                'checked' => in_array($key, $field) ? ' checked="checked"' : '',
                'checked_kqmt' => in_array($key, $field_kqmt) ? ' checked="checked"' : '',
                'checked_kqcgtt' => in_array($key, $field_kqcgtt) ? ' checked="checked"' : ''
            ));
            if (in_array($key, $hide_keys)) {
                $xtpl->parse('main.field.field_hide');
                $xtpl->parse('main.field_kqmt.field_hide');
                $xtpl->parse('main.field_kqcgtt.field_hide');
            }
            if ($key == 2) {
                $xtpl->parse('main.field.show_lct');
            }
            $xtpl->parse('main.field');
            $xtpl->parse('main.field_kqmt');
            $xtpl->parse('main.field_kqcgtt');
        }
        foreach ($arr_bidfieid as $k => $v) {
            $xtpl->assign('BIDFIEID', array(
                'key' => $k,
                'title' => $v,
                'checked' => in_array($k, $bidfieid) ? ' checked="checked"' : ''
            ));
            $xtpl->parse('main.bidfieid');
        }
        // Phương thức
        $phuong_thucs = [
            11 => $nv_Lang->getModule('type_lc_7'),
            12 => $nv_Lang->getModule('type_lc_8'),
            21 => $nv_Lang->getModule('type_lc_9'),
            22 => $nv_Lang->getModule('type_lc_10')
        ];
        foreach ($phuong_thucs as $k => $v) {
            $xtpl->assign('PHUONGTHUC', array(
                'key' => $k,
                'title' => $v,
                'checked' => in_array($k, $phuongthuc) ? ' checked="checked"' : ''
            ));
            $xtpl->parse('main.phuongthuc');
        }
        $sql = "SELECT lr.id, lr.title, GROUP_CONCAT(lp.id) AS province_ids
                FROM " . NV_PREFIXLANG . "_location_regions lr
                JOIN " . NV_PREFIXLANG . "_location_province lp ON lp.region_id = lr.id
                GROUP BY lr.id";
        $regions_list = $nv_Cache->db($sql, 'id', 'location_regions');
        $regions_list[0] = array(
            'id' => -1,
            'title' => $nv_Lang->getModule('undefined'),
            'alias' => strtolower(change_alias($nv_Lang->getModule('undefined'))),
            'province_ids' => ''
        );
        foreach ($regions_list as $key => $t) {
            $xtpl->assign('REGION', array(
                'key' => $key,
                'title' => $t['title'],
                'selected' => $key == $sregion ? ' selected="selected"' : '',
                'province_ids' => $t['province_ids']
            ));
            $xtpl->parse('main.loopregions');
            $xtpl->assign('REGION', array(
                'key' => $key,
                'title' => $t['title'],
                'selected' => $key == $sregion_plan ? ' selected="selected"' : '',
                'province_ids' => $t['province_ids']
            ));
            $xtpl->parse('main.show_province_plan.loopregions_plan');
        }
        $sql = "SELECT id, title, region_id, alias FROM " . NV_PREFIXLANG . "_location_province";
        $province_list = $nv_Cache->db($sql, 'id', 'location');
        $province_list[0] = array(
            'id' => 0,
            'title' => $nv_Lang->getModule('undefined'),
            'alias' => strtolower(change_alias($nv_Lang->getModule('undefined'))),
            'region_id' => null
        );
        $province_list = [
                825 => [
                    'id' => 825,
                    'title' => $nv_Lang->getModule('vn_out_territory'),
                    'alias' => change_alias($nv_Lang->getModule('alias_vn_out_territory')),
                    'region_id' => null
                ]
            ] + $province_list;

        $province_list = [
                824 => [
                    'id' => 824,
                    'title' => $nv_Lang->getModule('nationwide'),
                    'alias' => change_alias($nv_Lang->getModule('alias_nationwide')),
                    'region_id' => null
                ]
            ] + $province_list;
        foreach ($province_list as $key => $t) {
            $xtpl->assign('PROVINCE', array(
                'key' => $key,
                'title' => $t['title'],
                'selected' => $key == $sprovince ? ' selected="selected"' : '',
                'region_id' => $t['region_id']
            ));
            $xtpl->parse('main.province');
        }

        $oda_type = [
            0 => $nv_Lang->getModule('all'),
            1 => $nv_Lang->getModule('ODA_yes'),
            2 => $nv_Lang->getModule('ODA_no')
        ];
        foreach ($oda_type as $key => $value) {
            $xtpl->assign('oda_type', [
                'key' => $key,
                'selected' => ($key == $oda) ? ' selected="selected"' : '',
                'title' => $value
            ]);

            $xtpl->parse('main.oda_type');
        }

        $khlcnt_type = [
            0 => $nv_Lang->getModule('all'),
            1 => $nv_Lang->getModule('khlcnt_yes'),
            2 => $nv_Lang->getModule('khlcnt_no')
        ];
        foreach ($khlcnt_type as $key => $value) {
            $xtpl->assign('khlcnt_type', [
                'key' => $key,
                'selected' => ($key == $khlcnt) ? ' selected="selected"' : '',
                'title' => $value
            ]);

            $xtpl->parse('main.khlcnt_type');
        }

        if ($keyword_id_bidder > 0) {
            $sql = "SELECT id_bidder, name_bidder FROM " . $db_config['prefix'] . "_dau_gia_bidder WHERE id_bidder = " . $keyword_id_bidder . " ORDER BY name_bidder DESC";
            $result = $db->query($sql);
            $array_bidder = [];
            while ($row2 = $result->fetch()) {
                $array_bidder[$row2['id_bidder']] = $row2;
            }
            foreach ($array_bidder as $key => $bidder) {
                $xtpl->assign('BIDDER', array(
                    'key' => $key,
                    'name_bidder' => $bidder['name_bidder'],
                    "selected" => $key == $keyword_id_bidder ? 'selected="selected"' : ''
                ));
                $xtpl->parse('main.bidder');
            }
        }
        foreach ($province_list as $_province) {
            if ($_province['id'] == -1) {
                $_province['title'] = $nv_Lang->getModule('no_title');
                $_province['alias'] = 'Chua-phan-loai';
            }
            $xtpl->assign('PROVINCE', [
                'alias' => $_province['alias'],
                'key' => $_province['id'],
                'title' => $_province['title'],
                'selected' => in_array($_province['id'], $_idprovince) ? ' selected="selected"' : '',
                'region_id' => $_province['region_id']
            ]);
            $xtpl->parse('main.loopidprovince');
            $xtpl->assign('PROVINCE', [
                'key' => $_province['id'],
                'title' => $_province['title'],
                'selected' => in_array($_province['id'], $_idprovince_kq) ? ' selected="selected"' : '',
                'region_id' => $_province['region_id']
            ]);
            $xtpl->parse('main.loopidprovince_2');

            $xtpl->assign('PROVINCE_KHTT', [
                'key' => $_province['id'],
                'title' => $_province['title'],
                'selected' => in_array($_province['id'], $_idprovince_khtt) ? ' selected="selected"' : '',
                'region_id' => $_province['region_id']
            ]);
            $xtpl->parse('main.loopidprovince_3');

            $xtpl->assign('PROVINCE', [
                'key' => $_province['id'],
                'title' => $_province['title'],
                'selected' => in_array($_province['id'], $_idprovince_kh) ? ' selected="selected"' : '',
                'region_id' => $_province['region_id']
            ]);
            $xtpl->parse('main.show_province_plan.loopidprovince_plan');
        }
        if (!empty($rq_investor)) {
            foreach ($rq_investor as $value) {
                $xtpl->assign('RQ_INVESTOR', [
                    'key' => $value,
                    'title' => $value,
                    'selected' => ' selected="selected"'
                ]);
                $xtpl->parse('main.loop_ycbg');
            }
        }
        $xtpl->parse('main.show_province_plan');

        $advance_show = !empty($is_advance);
        $advance_btn_show = ($advance_show or ($type_search == 1 and in_array($type_info, [
                    1,
                    2,
                    3,
                    5,
                    7,
                    2,
                    15,
                    17,
                    22,
                    42
                ])) or ($type_search == 2 and ($type_info2 == 2 or $type_info2 == 4 or $type_info2 == 5)) or ($type_search == 3)) ? true : false;
        $advance_bl_show = ($advance_show || $advance_btn_show) ? true : false;
        // Luon đóng khi load trang
        // $advance_show = false;

        if (!$advance_bl_show) {
            $xtpl->parse('main.advance_bl_hide');
        }

        if (!$advance_btn_show) {
            $xtpl->parse('main.advance_btn_hide');
        }

        if ($advance_show) {
            $xtpl->assign('LANG_ADVANCE', $nv_Lang->getModule('search_simple'));
            $xtpl->assign('ADVANCE', 1);
            $xtpl->parse('main.advance_icon_1');
        } else {
            $xtpl->assign('LANG_ADVANCE', $nv_Lang->getModule('search_advance'));
            $xtpl->assign('ADVANCE', 0);
            $xtpl->parse('main.advance_icon_0');
            $xtpl->parse('main.advance_hide');
        }
        $typeContentHide = ($type_search == 2 or ($type_search == 1 and ($type_info == 4 or $type_info == 12 or $type_info == 10 or $type_info == 6 or $type_info == 13 or $type_info == 14 or $type_info == 15))) ? true : false;
        if ($typeContentHide) {
            $xtpl->parse('main.search_type_content');
            $xtpl->parse('main.search_kind');
        }

        if ($type_info == 17) {
            $xtpl->parse('main.search_type_content');
        }

        foreach ($array_type_htdauthau as $key => $value) {
            $xtpl->assign('HTLC_KEY', $key);
            $xtpl->assign('HTLC_TITLE', $value);
            $xtpl->assign('HTLC_SELECTED', (!empty($type_choose_id) and $key == $type_choose_id) ? 'selected' : '');
            $xtpl->parse('main.htluachon');
        }
        if ($type_info != 3) {
            $xtpl->parse('main.ls_htluachon_hide');
            $xtpl->parse('main.hide_type_kqlcnt_link');
            $xtpl->parse('main.hide_bidfieid');
        }

        foreach ($array_type_htdauthau as $key => $value) {
            $xtpl->assign('HTLC_KEY', $key);
            $xtpl->assign('HTLC_TITLE', $value);
            $xtpl->assign('HTLC_SELECTED', (!empty($type_choose_id) and $key == $type_choose_id) ? 'selected' : '');
            $xtpl->parse('main.htluachontb');
        }
        if ($type_info != 1) {
            $xtpl->parse('main.ls_htluachon_tb_hide');
        }

        if ($type_search == 2) {
            $xtpl->parse('main.type_org_hide');
            // $xtpl->parse('main.ls_cat_hide');
            $xtpl->parse('main.ls_goods_hide');
            $xtpl->parse('main.hide_phanmuc');
            $xtpl->parse('main.hide_vsic');
            $xtpl->parse('main.hide_province_kqlcnt');
            $xtpl->parse('main.hide_province_khttlcnt');
            $xtpl->parse('main.hide_goods_kqlcnt');
            $xtpl->parse('main.hide_tbmt_open_only');
            $xtpl->parse('main.no_dau_gia');
            $xtpl->parse('main.no_devproject');
            $xtpl->parse('main.no_kqmt');
            $xtpl->parse('main.no_kqcgtt');
            $xtpl->parse('main.hide_rq_form_value');
            $xtpl->parse('main.no_ycbg');
            $xtpl->parse('main.hide_type_kqlcnt_link');
            $xtpl->parse('main.hide_bidfieid');
            if ($type_info2 == 2) {
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
            } elseif ($type_info2 == 4) {
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.price_contract_hide');
            } elseif ($type_info2 == 5) {
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
            } else {
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
            }
        } else if ($type_search == 3) {
            $xtpl->parse('main.type_org_hide');
            $xtpl->parse('main.ls_cat_hide');
            $xtpl->parse('main.ls_goods_hide');
            $xtpl->parse('main.hide_phanmuc');
            $xtpl->parse('main.hide_vsic');
            $xtpl->parse('main.hide_province_kqlcnt');
            $xtpl->parse('main.hide_province_khttlcnt');
            $xtpl->parse('main.hide_goods_kqlcnt');
            $xtpl->parse('main.hide_tbmt_open_only');
            $xtpl->parse('main.no_khlcnt');
            $xtpl->parse('main.no_result');
            $xtpl->parse('main.no_tbmt');
            $xtpl->parse('main.no_kqmt');
            $xtpl->parse('main.no_kqcgtt');
            $xtpl->parse('main.price_contract_hide');
            $xtpl->parse('main.no_devproject');
            $xtpl->parse('main.hide_rq_form_value');
            $xtpl->parse('main.no_ycbg');
            $xtpl->parse('main.hide_type_kqlcnt_link');
            $xtpl->parse('main.hide_bidfieid');
            $xtpl->parse('main.show_typeinfo3_js');
            $xtpl->parse('main.hide_guide_search_1');
            $xtpl->parse('main.hide_guide_search_2');
        } else {
            $xtpl->parse('main.no_dau_gia');
            if ($type_info == 1) {
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.no_devproject');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.hide_province_khttlcnt');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_rq_form_value');
                $xtpl->parse('main.no_ycbg');
            } elseif ($type_info == 2) {
                // $xtpl->parse('main.ls_cat_hide');
                $xtpl->parse('main.ls_goods_hide');
                $xtpl->parse('main.hide_phanmuc');
                $xtpl->parse('main.hide_vsic');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.hide_province_khttlcnt');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_tbmt_open_only');
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.no_devproject');
                $xtpl->parse('main.hide_rq_form_value');
                $xtpl->parse('main.no_ycbg');
            } elseif ($type_info == 3) {
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_devproject');
                $xtpl->parse('main.ls_cat_hide');
                $xtpl->parse('main.hide_phanmuc');
                $xtpl->parse('main.hide_vsic');
                $xtpl->parse('main.hide_tbmt_open_only');
                $xtpl->parse('main.hide_rq_form_value');
                $xtpl->parse('main.no_ycbg');
                $xtpl->parse('main.hide_province_khttlcnt');
            } elseif ($type_info == 5) {
                $xtpl->parse('main.ls_cat_hide');
                $xtpl->parse('main.ls_goods_hide');
                $xtpl->parse('main.no_devproject');
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.hide_phanmuc');
                $xtpl->parse('main.hide_vsic');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_tbmt_open_only');
                $xtpl->parse('main.hide_rq_form_value');
                $xtpl->parse('main.no_ycbg');
                $xtpl->parse('main.hide_province_khttlcnt');
            } elseif ($type_info == 7) {
                $xtpl->parse('main.ls_cat_hide');
                $xtpl->parse('main.ls_goods_hide');
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.hide_phanmuc');
                $xtpl->parse('main.hide_vsic');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_tbmt_open_only');
                $xtpl->parse('main.hide_rq_form_value');
                $xtpl->parse('main.no_ycbg');
                $xtpl->parse('main.hide_province_khttlcnt');
            } elseif ($type_info == 15) {
                $xtpl->parse('main.ls_cat_hide');
                $xtpl->parse('main.ls_goods_hide');
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.hide_phanmuc');
                $xtpl->parse('main.hide_vsic');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.hide_tbmt_open_only');
                $xtpl->parse('main.hide_khlcnt_type');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_rq_form_value');
                $xtpl->parse('main.no_ycbg');
            } elseif ($type_info == 17) {
                $xtpl->parse('main.ls_cat_hide');
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.hide_phanmuc');
                $xtpl->parse('main.hide_tbmt_open_only');
                $xtpl->parse('main.hide_khlcnt_type');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.no_devproject');
                $xtpl->parse('main.hide_province_khttlcnt');
            } elseif ($type_info == 22) {
                $xtpl->parse('main.ls_cat_hide');
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.hide_phanmuc');
                $xtpl->parse('main.hide_tbmt_open_only');
                $xtpl->parse('main.hide_khlcnt_type');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.no_devproject');
                $xtpl->parse('main.hide_province_khttlcnt');
                $xtpl->parse('main.no_ycbg');
            } elseif ($type_info == 42) {
                $xtpl->parse('main.ls_cat_hide');
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.hide_phanmuc');
                $xtpl->parse('main.hide_tbmt_open_only');
                $xtpl->parse('main.hide_khlcnt_type');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.no_devproject');
                $xtpl->parse('main.hide_province_khttlcnt');
                $xtpl->parse('main.no_ycbg');
            } else {
                $xtpl->parse('main.no_tbmt');
                $xtpl->parse('main.no_kqmt');
                $xtpl->parse('main.no_kqcgtt');
                $xtpl->parse('main.no_khlcnt');
                $xtpl->parse('main.no_result');
                $xtpl->parse('main.hide_province_kqlcnt');
                $xtpl->parse('main.hide_goods_kqlcnt');
                $xtpl->parse('main.hide_rq_form_value');
                $xtpl->parse('main.no_ycbg');
                $xtpl->parse('main.hide_province_khttlcnt');
            }

            if (empty($_array_phanmuc_bids)) {
                $_sql = 'SELECT id, parent_id, title_' . NV_LANG_DATA . ' as title, alias_' . NV_LANG_DATA . ' as alias  FROM ' . BID_PREFIX_GLOBAL . '_phanmuc WHERE status=1 ORDER BY id ASC';
                $_array_listphanmucid = $nv_Cache->db($_sql, 'id', NV_LANG_DATA . '_' . $module_name);
                foreach ($_array_listphanmucid as $_row) {
                    $_array_phanmuc_bids[$_row['parent_id']][$_row['id']] = $_row;
                }
                if (!empty($_array_phanmuc_bids)) {
                    $_array_phanmuc_bids[0][-1] = array(
                        'id' => -1,
                        'parent_id' => 0,
                        'title' => $nv_Lang->getModule('no_title'),
                        'alias' => $nv_Lang->getModule('alias_chua_phan_loai')
                    );
                    $_array_phanmuc_bids[-1][0] = array(
                        'parent_id' => 0,
                        'title' => $nv_Lang->getModule('no_title'),
                        'alias' => $nv_Lang->getModule('alias_chua_phan_loai')
                    );
                }
            }
            $cache = $nv_Cache->getItem($module_name, NV_LANG_DATA . '_list_vsic.cache', '600');
            if ($cache !== 'false' && $cache != '') {
                // Nếu đã lưu cache thì đọc luôn
                $list_code_vsic = json_decode($cache, 'true');
            } else {
                $_sql = 'SELECT id, level, code, title, alias FROM ' . NV_PREFIXLANG . '_industry WHERE status = 1 ORDER BY id ASC';
                $res_vsic = $db->query($_sql);
                while ($row_vsic = $res_vsic->fetch()) {
                    $row_vsic['link'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . ($module_info['alias']['vsic-industry'] ?? '') . '/' . $row_vsic['alias'] . '-' . $row_vsic['code'];
                    $list_code_vsic[$row_vsic['code']] = $row_vsic;
                }
                $res_vsic->closeCursor();
                $cache_json = json_encode($list_code_vsic);
                $nv_Cache->setItem($module_name, NV_LANG_DATA . '_list_vsic.cache', $cache_json, 600);
            }
            $list_code_vsic[0] = [
                'id' => 0,
                'code' => 0,
                'level' => 1,
                'title' => mb_convert_case($nv_Lang->getModule('no_title'), MB_CASE_UPPER),
                'alias' => $nv_Lang->getModule('alias_chua_phan_loai')
            ];
            if (!empty($_array_phanmuc_bids[0])) {
                foreach ($_array_phanmuc_bids[0] as $phanmuc_id => $phanmuc_data) {
                    $xtpl->assign('PHANMUC_MAIN', [
                        'key' => $phanmuc_data['id'],
                        'title' => $phanmuc_data['title']
                    ]);

                    if (isset($_array_phanmuc_bids[$phanmuc_id])) {
                        foreach ($_array_phanmuc_bids[$phanmuc_id] as $phanmuc_subid => $phanmucsub_data) {
                            $xtpl->assign('PHANMUC_SUB', [
                                'key' => $phanmuc_subid,
                                'title' => $phanmucsub_data['title'],
                                'selected' => in_array($phanmuc_subid, $_phanmucid) ? ' selected="selected"' : ''
                            ]);
                            $xtpl->parse('main.loopphanmuc1.loopphanmuc2');
                        }
                    }
                    $xtpl->parse('main.loopphanmuc1');
                }
            }
            foreach ($list_code_vsic as $v) {
                $v['selected'] = in_array($v['code'], $vsic_code) ? ' selected="selected"' : '';
                $xtpl->assign('VSIC', $v);
                if (!empty($v['code'])) {
                    $xtpl->parse('main.loopvsic.is_code');
                }
                $xtpl->parse('main.loopvsic');
            }

            if (empty($loai_cong_trinhs)) {
                $loai_cong_trinhs = [
                    1 => $nv_Lang->getModule('CTGG'),
                    2 => $nv_Lang->getModule('CTYT'),
                    3 => $nv_Lang->getModule('CTTT'),
                    4 => $nv_Lang->getModule('CTVH'),
                    5 => $nv_Lang->getModule('CHO'),
                    6 => $nv_Lang->getModule('CTTG'),
                    7 => $nv_Lang->getModule('TTT'),
                    8 => $nv_Lang->getModule('CTSXSP'),
                    9 => $nv_Lang->getModule('CTLKCK'),
                    10 => $nv_Lang->getModule('CTKTCB'),
                    11 => $nv_Lang->getModule('CTDK'),
                    12 => $nv_Lang->getModule('CTNL'),
                    13 => $nv_Lang->getModule('CTHC'),
                    14 => $nv_Lang->getModule('CTCNN'),
                    15 => $nv_Lang->getModule('CTCN'),
                    16 => $nv_Lang->getModule('CTTN'),
                    17 => $nv_Lang->getModule('CTXLCT'),
                    18 => $nv_Lang->getModule('CVCX'),
                    19 => $nv_Lang->getModule('NT'),
                    20 => $nv_Lang->getModule('NTL'),
                    21 => $nv_Lang->getModule('CSHT'),
                    22 => $nv_Lang->getModule('NSMT'),
                    23 => $nv_Lang->getModule('DCTTH'),
                    24 => $nv_Lang->getModule('CTDB'),
                    25 => $nv_Lang->getModule('CTDS'),
                    26 => $nv_Lang->getModule('CTCAU'),
                    27 => $nv_Lang->getModule('CTDTND'),
                    28 => $nv_Lang->getModule('CTHH'),
                    29 => $nv_Lang->getModule('CTHK'),
                    30 => $nv_Lang->getModule('CTTL'),
                    31 => $nv_Lang->getModule('CTDD'),
                    32 => $nv_Lang->getModule('KHAC')
                ];
            }

            foreach ($loai_cong_trinhs as $key => $value) {
                $xtpl->assign('LOAICONGTRINH', [
                    'key' => $key,
                    'title' => $value,
                    'selected' => in_array($key, $_lct) ? ' selected="selected"' : ''
                ]);
                $xtpl->parse('main.loop_lct');
            }
        }

        $xtpl->assign('LANG_DOCUMENT_URL', ($type_search == 3 ? DAUGIA_DOMAIN . $nv_Lang->getModule('link_documentation_for_contractors_daugia') : DAUTHAU_INFO_DOMAIN . $nv_Lang->getModule('link_documentation_for_contractors')));
        $xtpl->assign('LANG_DOCUMENT_TEXT', $type_search == 3 ? $nv_Lang->getModule('filter_slogan_daugia') : $nv_Lang->getModule('filter_slogan'));

        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    global $nv_Cache, $site_mods, $module_name;
    $module = $block_config['module'];

    if (isset($site_mods[$module])) {
        $content = nv_bidding_block_search($block_config);
    }
}
