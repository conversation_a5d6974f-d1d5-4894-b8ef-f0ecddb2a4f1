<?php

use function Composer\Autoload\includeFile;
use NukeViet\Dauthau\Share;
use NukeViet\Module\bidding\Shared\ViolateMsg;

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 3/9/2010 23:25
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}
use NukeViet\Dauthau\Url;

if (!nv_function_exists('nv_bidding_tinhthanh')) {

    function nv_bidding_tinhthanh($block_config)
    {
        global $db, $nv_Lang, $global_config, $type_user, $nv_Request, $site_mods, $module_config, $nv_Cache, $array_op, $op;
        $_module_name = 'bidding';
        $_module_info = $site_mods[$_module_name];
        $_module_data = $_module_info['module_data'];
        $_module_file = $_module_info['module_file'];
        $nv_Lang->changeLang();
        $nv_Lang->loadModule($_module_file, false, true);
        if (file_exists(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/bidding/block_tinhthanh.tpl')) {
            $block_theme = $global_config['module_theme'];
        } else {
            $block_theme = 'default';
        }
        $xtpl = new XTemplate('block_tinhthanh.tpl', NV_ROOTDIR . '/themes/' . $block_theme . '/modules/bidding');
        $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
        $xtpl->assign('MODULE_NAME', $_module_name);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$tmplang_module);
        $xtpl->assign('LOCALE', NV_LANG_DATA == 'vi' ? 'vi-VN' : 'en-US');
        $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
        $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
        $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
        $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
        $xtpl->assign('TEMPLATE', $block_theme);
        $xtpl->assign('OP', $op);
        $xtpl->assign('TYPE_USER', $type_user);
        $xtpl->assign('LOCALE', NV_LANG_DATA == 'vi' ? 'vi-VN' : 'en-US');
        $xtpl->assign('LINK_JS', '/themes/' . $block_theme . '/');
        $arr_title_province = [
            815, //Tp cần thơ
            501, //đà nẵng
            101, //hà nội
            103, //hải phòng
            701 //HCM
        ];

        $array_data = [];
        $arr_unexist_key = [];
        $error = [];

        $type_search = $nv_Request->get_int('type_search', 'post,get', 1); // 1. Lựa chọn nhà thầu; 2. Lựa chọn nhà đầu tư
        $type_info = $nv_Request->get_int('type_info', 'post,get', 1);
        $type_info2 = $nv_Request->get_int('type_info2', 'post,get', 1);
        $sfrom = nv_substr($nv_Request->get_title('sfrom', 'get', ''), 0, 10); // thời gian
        $sto = nv_substr($nv_Request->get_title('sto', 'get', ''), 0, 10);
        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sto, $m)) {
            $sto1 = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
        } else {
            $sto1 = NV_CURRENTTIME;
        }
        $sto1 > NV_CURRENTTIME && $sto1 = NV_CURRENTTIME;

        $_m = [];
        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sfrom, $m)) {
            $sfrom1 = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
        } else {
            $sfrom1 = 0;
        }
        $sfrom1 > NV_CURRENTTIME && $sfrom1 = NV_CURRENTTIME;
        $sto2 = $sto1 - 3600;
        $sfrom1 > $sto2 && $sfrom1 = $sto2;
        $_idprovince = $nv_Request->get_int('idprovince', 'get,post', -1); //Tỉnh thành
        $active_province = [];
        $is_elas = ($module_config['bidding']['elas_use']) ? true : false;
        $array_data = $arr_solicitor_id = [];
        if (!empty($array_op[1])) {
            //preg_match('/^([A-Za-z0-9\-]+)\-([0-9]+)$/',$array_op[1], $m)
            if (preg_match('/^([A-Za-z0-9\-]+)$/', $array_op[1], $m)) {
                if ($m[0] == 'Chua-phan-loai') {
                    $active_province[] = array('id' => 0, 'images' => NV_BASE_SITEURL . NV_UPLOADS_DIR . '/province/tinhthanh.jpg', 'description' => $nv_Lang->getModule('description_province'), 'title' => $nv_Lang->getModule('no_title'), 'alias' => 'Chua-phan-loai');
                    $_temppro = array('id' => 0, 'images' => NV_BASE_SITEURL . NV_UPLOADS_DIR . '/province/tinhthanh.jpg', 'description' => $nv_Lang->getModule('description_province'), 'title' => $nv_Lang->getModule('no_title'), 'alias' => 'Chua-phan-loai');
                    $_idprovince = 0;
                } else {
                    $_temppro = $db->query('SELECT id, images, description, title, alias FROM ' . NV_PREFIXLANG . '_location_province WHERE alias = ' . $db->quote($m[0]))->fetch();
                    if (!empty($_temppro)) {
                        $active_province[] = $_temppro;
                        $_idprovince = $_temppro['id'];
                    } else {
                        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['tinh-thanh']);
                    }
                }
            }
        } else {
            $active_province[] = array('id' => -1, 'images' => NV_BASE_SITEURL . NV_UPLOADS_DIR . '/province/tinhthanh.jpg', 'description' => $nv_Lang->getModule('description_province'), 'title' => $nv_Lang->getModule('no_title'), 'alias' => '');
            $_idprovince = -1;
        }
        $array_data = [];
        if ($is_elas) {
            $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$_module_name]['elas_host'], $module_config[$_module_name]['elas_port'], NV_LANG_ELASTIC . 'dauthau_bidding', $module_config[$_module_name]['elas_user'], $module_config[$_module_name]['elas_pass']);

            isset($l) && $l === 0 && $search_kind = 1; // Kiểu tìm kiếm 1. Khớp từ hoặc một số từ 2. Khớp tất cả từ 0.Khớp chính xác cụm từ
            $par_search = $par_search ?? 0; // Chỉ tìm theo tên hoặc mã
            $goods_search = $goods_search ?? 0; // Tìm kiếm hàng hóa: 0. Không tìm kiếm theo hàng hóa 1.Tìm kiếm mở rộng vào tên hàng hóa 2.Chỉ tìm kiếm theo tên hàng hóa
            $open_only = $open_only ?? 0; // Chỉ tìm kiếm TBMT chưa đóng thầu
            $search_elastic = [];
            if ($sfrom1 > 0 and $sto1 > 0) {
                $search_elastic['must'][]['range']['ngay_dang_tai'] = [
                    "gte" => $sfrom1,
                    "lte" => $sto1
                ];
            }            
            // Tìm kiếm theo tỉnh thành
            if ($_idprovince > 0) {
                $search_idprovince = array();
                if ($_idprovince == 0) {
                    $search_idprovince[] = [
                        'term' => [
                            'province_id' => 0
                        ]
                    ];
                } else {
                    $search_idprovince[] = [
                        'regexp' => [
                            'province_id' => [
                                'value' => '~[0-9]' . $_idprovince . '~[0-9]',
                                'flags' => 'ALL'
                            ]
                        ]
                    ];
                }
                $search_elastic['must'][]['bool']['should'] = $search_idprovince;
            }
            
            $array_query_elastic = array();
            if (!empty($search_elastic)) {
                $array_query_elastic['query']['bool'] = $search_elastic;
            }
            $per_page = 10;
            $array_query_elastic['track_total_hits'] = 'true';
            $array_query_elastic['size'] = $per_page;
            $array_query_elastic['sort'] = [
                [
                    "ngay_dang_tai" => [
                        "order" => "desc"
                    ]
                ]
            ];
            try {
                $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $_module_data . '_row', $array_query_elastic);
            } catch (Exception $e) {
                file_put_contents(NV_ROOTDIR . '/data/logs/detailelastic_log_' . date('Ymd') . '.txt', "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi detailelastic_log_: \n" . print_r($e, true), FILE_APPEND);
                file_put_contents(NV_ROOTDIR . '/data/logs/detailelastic_log_' . date('Ymd') . '.txt', "\n array_query_elastic: " . print_r($array_query_elastic, true), FILE_APPEND);
                trigger_error(print_r($e, true));
            }
            $num_items = $response['hits']['total']['value'];

            foreach ($response['hits']['hits'] as $value) {
                if (!empty($value['_source'])) {
                    $view = $value['_source'];
                    $view['den_ngay'] = $view['den_ngay'] > 0 ? nv_date('H:i d/m/Y', $view['den_ngay']) : '';
                    $view['get_time'] = $view['get_time'] > 0 ? nv_date('H:i d/m/Y', $view['get_time']) : '';
                    $view['ngay_dang_tai'] = $view['ngay_dang_tai'] ? nv_date('H:i d/m/Y', $view['ngay_dang_tai']) : '';
                    $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['view'] . '/' . ($type_search == 1 ? Url::getTBMT() : Url::getTBMDT()) . '/' . $view['alias'] . '-' . $view['id'] . $global_config['rewrite_exturl'];
                    $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=detail&solicitor_id=' . $view['solicitor_id'];

                    $array_data[$view['id']] = $view;
                    $arr_solicitor_id[$view['solicitor_id']] = $view['solicitor_id'];
                }
            }

        } else {
            
            $where = 'ngay_dang_tai>=' . $sfrom1 . ' AND ngay_dang_tai<=' . $sto1;
            if ($_idprovince > 0) {
                if ($_idprovince == 0) {
                    $where .= ' AND ( province_id = 0)';
                } else {
                    $find_province = [];
                    $find_province[] = "FIND_IN_SET(" . $_idprovince . ", province_id)";
            
                    if (!empty($find_province)) {
                        $where .= " AND (" . implode(' OR ', $find_province) . ")";
                    }
                }
            }
            $db->sqlreset()
                ->select('COUNT(id)')
                ->from(NV_PREFIXLANG . '_' . $_module_data . '_row')
                ->where($where);
            $sth = $db->prepare($db->sql());
            $sth->execute();
            $num_items = $sth->fetchColumn();

            $sl_alias = $type_search == 2 ? '' : ', alias';
            $per_page = 10;
            $db->select('*' . $sl_alias)
                ->order('ngay_dang_tai DESC')
                ->limit($per_page);
            $sth = $db->prepare($db->sql());
            $sth->execute();
            while ($view = $sth->fetch()) {
                $view['den_ngay'] = $view['den_ngay'] > 0 ? nv_date('H:i d/m/Y', $view['den_ngay']) : '';
                $view['get_time'] = $view['get_time'] > 0 ? nv_date('H:i d/m/Y', $view['get_time']) : '';
                $view['ngay_dang_tai'] = $view['ngay_dang_tai'] ? nv_date('H:i d/m/Y', $view['ngay_dang_tai']) : '';
            
                if ($type_search == 2) {
                    $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $global_config['module_bidding_name'] . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['view'] . '/' . Url::getTBMDT() . '/' . strtolower(change_alias($view['goi_thau'])) . '-' . $view['id'] . $global_config['rewrite_exturl'];
                } else {
                    $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $global_config['module_bidding_name'] . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['view'] . '/' . Url::getTBMT() . '/' . $view['alias'] . '-' . $view['id'] . $global_config['rewrite_exturl'];
                }
            
                $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $global_config['module_bidding_name'] . '&' . NV_OP_VARIABLE . '=detail&solicitor_id=' . $view['solicitor_id'];
            
                $array_data[$view['id']] = $view;
                $arr_solicitor_id[$view['solicitor_id']] = $view['solicitor_id'];
            }
        }
        
        $list_solicitor = [];
        if (!empty($array_data)) {
            // Lấy alias nhà mời thầu
            $solicitor_list = implode(',', $arr_solicitor_id);
            $db->sqlreset();
            $db->select('id,alias,solicitor_code, org_code, english_name')
                ->from(BID_PREFIX_GLOBAL . '_solicitor')
                ->where('id IN(' . $solicitor_list . ')');
            $sth = $db->prepare($db->sql());
            $sth->execute();
            while ($info_solicitor = $sth->fetch()) {
                $list_solicitor[$info_solicitor['id']] = [
                    $info_solicitor['alias'],
                    (!empty($info_solicitor['org_code']) ? $info_solicitor['org_code'] : $info_solicitor['solicitor_code']),
                    $info_solicitor['english_name']
                ];
            }
        
            foreach ($array_data as $key => $data) {
                if (isset($list_solicitor[$data['solicitor_id']])) {
                    $data['alias_solicitor'] = $list_solicitor[$data['solicitor_id']][0];
                    $array_data[$key]['solicitor_code'] = trim($list_solicitor[$data['solicitor_id']][1]);
                    // alias đa ngôn ngữ
                    $list_solicitor[$data['solicitor_id']][2] = !empty($list_solicitor[$data['solicitor_id']][2]) ? trim($list_solicitor[$data['solicitor_id']][2]) : '';
                    if (NV_LANG_DATA != 'vi' && !empty($list_solicitor[$data['solicitor_id']][2])) {
                        $data['alias_solicitor'] = change_alias($list_solicitor[$data['solicitor_id']][2]);
                        $array_data[$key]['ben_moi_thau'] = $list_solicitor[$data['solicitor_id']][2];
                    }
                } else {
                    $data['alias_solicitor'] = change_alias($data['ben_moi_thau']);
                }
                $array_data[$key]['link_solicitor'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['solicitor'] . '/' . $data['alias_solicitor'] . '-' . $data['solicitor_id'];
            }
        }
        $color_map = [
            'dtrr' => '#2ecc71',
            'chct' => '#3498db',
            'chctrg' => '#1e90ff',
            'dthc' => '#9b59b6',
            'mstt' => '#ff6347',
            'cdt' => '#34495e',
            'cdtrg' => '#e67e22',
            'tth' => '#ff4500',
            'lcnt_db' => '#32cd32',
            'tvcn' => '#ffd700',
            'tctvcn' => '#8a2be2',
            'dpct' => '#00ced1',
            'qcbs' => '#ff69b4',
            'dpg' => '#b22222',
            'qbs' => '#4682b4',
            'fbs' => '#d2691e',
            'lcs' => '#9932cc',
            'cqs' => '#7fff00',
            'sss' => '#ff1493',
            'tgtc' => '#1e90ff',
            'nhbd' => '#ff4500',
            'tvct' => '#32cd32',
            'tgthcd' => '#ffd700',
        ];
        $dm_htlcnt = [];
        $dm_htlcnt['DTRR'] = $nv_Lang->getModule('type_lc_17');
        $dm_htlcnt['CHCT'] = $nv_Lang->getModule('type_lc_12');
        $dm_htlcnt['CHCTRG'] = $nv_Lang->getModule('type_lc_11');
        $dm_htlcnt['DTHC'] = $nv_Lang->getModule('type_lc_16');
        $dm_htlcnt['MSTT'] = $nv_Lang->getModule('type_lc_19');
        $dm_htlcnt['CDT'] = $nv_Lang->getModule('type_lc_14');
        $dm_htlcnt['CDTRG'] = $nv_Lang->getModule('type_lc_13');
        $dm_htlcnt['TTH'] = $nv_Lang->getModule('type_lc_23');
        $dm_htlcnt['LCNT_DB'] = $nv_Lang->getModule('type_lc_30');
        $dm_htlcnt['TVCN'] = $nv_Lang->getModule('type_lc_31');
        $dm_htlcnt['TCTVCN'] = $nv_Lang->getModule('type_lc_28');
        $dm_htlcnt['DPCT'] = $nv_Lang->getModule('type_lc_32');
        $dm_htlcnt['QCBS'] = $nv_Lang->getModule('type_lc_33');
        $dm_htlcnt['DPG'] = $nv_Lang->getModule('type_lc_15');
        $dm_htlcnt['QBS'] = $nv_Lang->getModule('type_lc_34');
        $dm_htlcnt['FBS'] = $nv_Lang->getModule('type_lc_35');
        $dm_htlcnt['LCS'] = $nv_Lang->getModule('type_lc_36');
        $dm_htlcnt['CQS'] = $nv_Lang->getModule('type_lc_37');
        $dm_htlcnt['SSS'] = $nv_Lang->getModule('type_lc_38');
        $dm_htlcnt['TGTC'] = $nv_Lang->getModule('type_lc_39');
        $dm_htlcnt['NHBD'] = $nv_Lang->getModule('type_lc_40');
        $dm_htlcnt['TVCT'] = $nv_Lang->getModule('type_lc_41');
        $dm_htlcnt['TGTHCD'] = $nv_Lang->getModule('type_lc_20');
        $province_data = array();
        if ($_idprovince == -1) {
            $query_st = $db->query("SELECT id_province, statistics FROM nv4_province_statistics WHERE id_province IN (101, 501, 701)");
            if (!empty($query_st)) {
                $rows_statistics = $query_st->fetchAll();
                array_map(function ($row) use (&$province_data, $color_map, $dm_htlcnt) {
                    $id_province = $row['id_province'];
                    $statistics = json_decode($row['statistics'], true);
                    foreach ($statistics as $key => $value) {
                        if ($value > 0 && $key != 'id_province') {
                            $label = $dm_htlcnt[strtoupper($key)];
                            $province_data[$id_province]['values'][] = (int)$value;
                            $province_data[$id_province]['colors'][] = $color_map[$key];
                            $province_data[$id_province]['labels'][] = $label;
                        }
                    }
                }, $rows_statistics);
            }
        }
        $array_province = [];
        $t_cache_province = !empty($array_op[1]) ? $array_op[1] : 'tinhthanh';
        // Tên file cache
        $cache_province_file = 'cache_province_' . $t_cache_province . '_' . NV_CACHE_PREFIX . '.cache';

        // Kiểm tra xem dữ liệu đã được cache chưa
        // Cache trong 1 tiếng (3600 giây)
        if (($cache_province = $nv_Cache->getItem($_module_name, $cache_province_file, 3600)) != false) {
            $array_province = unserialize($cache_province);
        } else {
            $result = $db->query('SELECT * FROM nv4_province_statistics WHERE id_province = ' . $_idprovince);
            $rows = $result->fetch();
            if ($_idprovince > 0) {
                $statistics = json_decode($rows['statistics'], true);
                $id_province =  $_idprovince;
                foreach ($statistics as $key => $value) {
                    if ($value > 0) {
                        $label = $dm_htlcnt[strtoupper($key)];
                        $province_data[$id_province]['values'][] = (int)$value;
                        $province_data[$id_province]['colors'][] = $color_map[$key];
                        $province_data[$id_province]['labels'][] = $label;
                    }
                }
            }

            if (!empty($rows)) {
                // Nếu có dữ liệu trong bảng
                $array_province = array(
                    'total_province' => $rows['total_province'],
                    'total_dadtpt' => $rows['total_dadtpt'],
                    'total_plans' => $rows['total_plans'],
                    'total_kqlcnt' => $rows['total_kqlcnt'],
                    'total_nhathau' => $rows['total_nhathau'],
                    'total_bmt' => $rows['total_bmt'],
                    'total_las' => $rows['total_las'],
                    'total_tochuc' => $rows['total_tochuc'],
                    'total_project' => $rows['total_project'],
                    'total_contract' => $rows['total_contract'],
                    'total_vilas' => $rows['total_vilas'],
                    'total_vicas' => $rows['total_vicas'],
                    'total_vias' => $rows['total_vias'],
                    'total_vilas_med' => $rows['total_vilas_med'],
                    'total_dau_gia' => $rows['total_dau_gia'],
                    'total_dau_gia_select' => $rows['total_dau_gia_select'],
                    'total_dau_gia_bidder' => $rows['total_dau_gia_bidder'],
                    'total_dau_gia_dgv' => $rows['total_dau_gia_dgv'],
                    'total_dau_gia_deparment' => $rows['total_dau_gia_deparment'],
                    'total_stocks' => $rows['total_stocks'],
                    'total_plans_overall' => $rows['total_plans_overall'],
                    'total_mstnht' => $rows['total_mstnht'],
                    'total_moiquantam' => $rows['total_moiquantam'],
                    'total_ketquasotuyen' => $rows['total_ketquasotuyen'],
                    'total_ketquamoiquantam' => $rows['total_ketquamoiquantam'],
                    'total_open' => $rows['total_open'],
                    'total_ketquamosotuyen' => $rows['total_ketquamosotuyen'],
                    'total_ketquamoquantam' => $rows['total_ketquamoquantam'],
                    'total_hanghoa' => $rows['total_hanghoa'],
                    'total_ntvp' => $rows['total_ntvp'],
                    'total_cbda' => $rows['total_cbda'],
                    'total_khlcndt' => $rows['total_khlcndt'],
                    'total_moisotuyen' => $rows['total_moisotuyen'],
                    'total_kqst_project' => $rows['total_kqst_project'],
                    'total_kqlcndt' => $rows['total_kqlcndt'],
                    'total_vbdt' => $rows['total_vbdt'],
                    'total_vipas' => $rows['total_vipas'],
                    'total_viras' => $rows['total_viras'],
                    'total_moidautu' => $rows['total_moidautu']
                    
                );
            } else {
                // Nếu không có dữ liệu trong bảng, gán mảng rỗng
                $array_province = array(
                    'total_province' => 0,
                    'total_dadtpt' => 0,
                    'total_plans' => 0,
                    'total_kqlcnt' => 0,
                    'total_nhathau' => 0,
                    'total_bmt' => 0,
                    'total_las' => 0,
                    'total_tochuc' => 0,
                    'total_project' => 0,
                    'total_contract' => 0,
                    'total_vilas' => 0,
                    'total_vicas' => 0,
                    'total_vias' => 0,
                    'total_vilas_med' => 0,
                    'total_dau_gia' => 0,
                    'total_dau_gia_select' => 0,
                    'total_dau_gia_bidder' => 0,
                    'total_dau_gia_dgv' => 0,
                    'total_dau_gia_deparment' => 0,
                    'total_stocks' => 0,
                    'total_plans_overall' => 0,
                    'total_mstnht' => 0,
                    'total_moiquantam' => 0,
                    'total_ketquasotuyen' => 0,
                    'total_ketquamoiquantam' => 0,
                    'total_open' => 0,
                    'total_ketquamosotuyen' => 0,
                    'total_ketquamoquantam' => 0,
                    'total_hanghoa' => 0,
                    'total_ntvp' => 0,
                    'total_cbda' => 0,
                    'total_khlcndt' => 0,
                    'total_moisotuyen' => 0,
                    'total_kqst_project' => 0,
                    'total_kqlcndt' => 0,
                    'total_vbdt' => 0,
                    'total_vipas' => 0,
                    'total_viras' => 0,
                    'total_moidautu' => 0
                    
                );
            }
            $cache_province = serialize($array_province);
            $nv_Cache->setItem($_module_name, $cache_province_file, $cache_province);
        }
        $sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province ORDER BY title ASC";
        $province_list = $nv_Cache->db($sql, 'id', 'location_bidding');
        $province_list[0] = array(
            'id' => 0,
            'title' => $nv_Lang->getModule('no_title'),
            'alias' => 'Chua-phan-loai'
        );
        $key = 0;
        $array_province_list = $statistics_labels = $statistics_colors = $statistics_values = [];
        $class_current = '';
        $class_mobile = '';
        if (empty($array_op[1])) {
            $class_current = 'current';
            $class_mobile = 'province_mobile';
        }
        foreach ($province_list as $t) {
            ++$key;
            $active = '';
            if (!empty($array_op[1])) {
                $active = $array_op[1];
            }
            // Gán thông tin của từng tỉnh thành cho biến PROVINCE
            $xtpl->assign('PROVINCE', array(
                'id' => $t['id'],
                'title' => $t['title'],
                'link' => '/' . $nv_Lang->getModule('linktinhthanh') . '/' . $t['alias'] . '/',
                'selected' => $t['alias'] == $active ? ' selected="selected"' : ''
            ));
            $xtpl->parse('main.province.part');
            // Lấy id của tỉnh thành và gán mảng thông tin tỉnh thành cho biến PROVINCE_LIST_id_province
            $id_province = $t['id'];
            $array_province_list[$id_province] = array(
                'id' => $t['id'],
                'title' => $t['title'],
                'link' => '/' . $nv_Lang->getModule('linktinhthanh') . '/' . $t['alias'] . '/',
                'current' => $class_current
            );
            $xtpl->assign('PROVINCE_LIST_' . $id_province, $array_province_list[$id_province]);
        }
        // Phân tích khối main.province.province_list
        $xtpl->parse('main.province.province_list');
        $active_province['thumb'] = NV_BASE_SITEURL . NV_UPLOADS_DIR . '/province/tinhthanh.jpg';
        $active_province['description'] = $nv_Lang->getModule('description_province');
        $province_id = 0;
        $active_province['link_devprojects'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=devprojects&home_page=1';
        $active_province['link_listplan'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['plans'] . '/' . Url::getKHLCNT() . '&home_page=1';
        $active_province['link_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['view'] . '/' . Url::getTBMT() . '&home_page=1';
        $active_province['link_listresult'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['result'] . '/' . Url::getKQLCNT() . '&home_page=1';
        $active_province['link_planndt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['plans'] . '/' . Url::getKHLCNDT() . '&home_page=1';
        $active_province['link_kqlcndt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['result'] . '/' . Url::getKQLCNDT() . '&home_page=1';
        $active_province['link_nhathau'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=businesslistings';
        $active_province['link_ntvipham'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['nha-thau-vi-pham'] . '&home_page=1';
        $active_province['link_bmt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=procuring-entity&home_page=1';
        $active_province['link_las'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=las&home_page=1';
        $active_province['link_tcxd'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=to-chuc&home_page=1';
        $active_province['link_vilas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vilas&home_page=1';
        $active_province['link_vicas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vicas&home_page=1';
        $active_province['link_vias'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vias&home_page=1';
        $active_province['link_vilas_med'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vilas-med&home_page=1';
        $active_province['link_vipas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vipas&home_page=1';
        $active_province['link_viras'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=viras&home_page=1';
        $active_province['link_dau_gia'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia&home_page=1';
        $active_province['link_dau_gia_select'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia' . '&' . NV_OP_VARIABLE . '=bidorganization&home_page=1';
        $active_province['link_dau_gia_bidder'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia' . '&' . NV_OP_VARIABLE . '=organization&home_page=1';
        $active_province['link_dau_gia_dgv'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia' . '&' . NV_OP_VARIABLE . '=' . $site_mods['dau-gia']['alias']['auctioneer'] . '&home_page=1';
        $active_province['link_dau_gia_department'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia' . '&' . NV_OP_VARIABLE . '=department&home_page=1';
        $active_province['link_stocks'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . 'businesslistings' . '&' . NV_OP_VARIABLE . '=stocks&home_page=1';
        $active_province['link_qhxddt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['quyhoach'] . '&home_page=1';
        $active_province['link_plans_overall'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['planoverall'] . '/' . Url::getKHTTLCNT() . '&home_page=1';
        $active_province['link_mstnht'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['prequalification'] . '/' . Url::getNT() . '&home_page=1';
        $active_province['link_moiquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . Url::getMQTNT() . '/' . Url::getNT1() . '&home_page=1';
        $active_province['link_ketquasotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['resultpq'] . '/' . Url::getNT() . '&home_page=1';
        $active_province['link_ketquamoiquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . Url::getKQMQTNT() . '/' . Url::getNT1() . '&home_page=1';
        $active_province['link_open'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['open'] . '&home_page=1';
        $active_province['link_ketquamosotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['viewopenpq'] . '/' . Url::getNT() . '&home_page=1';
        $active_province['link_ketquamoquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . Url::getKQMHSQTNT() . '/' . Url::getNT() . '&home_page=1'; 
        $active_province['link_hanghoa'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['hanghoa'] . '&home_page=1'; 
        $active_province['link_cbda'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=project&home_page=1'; 
        $active_province['link_moisotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['prequalification'] . '/' . Url::getNDT() . '&home_page=1'; 
        $active_province['link_kqst_project'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['resultpq'] . '/' . Url::getNDT() . '&home_page=1'; 
        $laws_module_name = NV_LANG_DATA == 'vi' ? 'van-ban-dau-thau' : 'legal-documents';
        $active_province['link_vbdt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $laws_module_name. '/search&area=5' . '&home_page=1'; 
        $active_province['link_moidautu'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' .$_module_info['alias']['view'] . '/' . Url::getTBMDT() . '&home_page=1'; 

        $active_province['link__devprojects'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=devprojects';
        $active_province['link__listplan'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['plans'] . '/' . Url::getKHLCNT();
        $active_province['link__detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['view'] . '/' . Url::getTBMT();
        $active_province['link__listresult'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['result'] . '/' . Url::getKQLCNT();
        $active_province['link__planndt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['plans'] . '/' . Url::getKHLCNDT();
        $active_province['link__kqlcndt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['result'] . '/' . Url::getKQLCNDT();
        $active_province['link__nhathau'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings';
        $active_province['link__ntvipham'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['nha-thau-vi-pham'];
        $active_province['link__bmt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=procuring-entity';
        $active_province['link__las'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=las';
        $active_province['link__tcxd'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=to-chuc';
        $active_province['link__vilas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vilas';
        $active_province['link__vicas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vicas';
        $active_province['link__vias'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vias';
        $active_province['link__vilas_med'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vilas-med';
        $active_province['link__vipas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vipas';
        $active_province['link__viras'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=viras';
        $active_province['link__dau_gia'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia';
        $active_province['link__dau_gia_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/';
        $active_province['link__dau_gia_select'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia' . '&amp;' . NV_OP_VARIABLE . '=bidorganization';
        $active_province['link__dau_gia_select_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/bidorganization/';
        $active_province['link__dau_gia_bidder'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia' . '&amp;' . NV_OP_VARIABLE . '=organization';
        $active_province['link__dau_gia_bidder_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/organization/';
        $active_province['link__dau_gia_dgv'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia' . '&amp;' . NV_OP_VARIABLE . '=' . $site_mods['dau-gia']['alias']['auctioneer'];
        $active_province['link__dau_gia_dgv_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $site_mods['dau-gia']['alias']['auctioneer'] . '/';
        $active_province['link__dau_gia_department'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia' . '&amp;' . NV_OP_VARIABLE . '=department';
        $active_province['link__dau_gia_department_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/department/';
        $active_province['link__stocks'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . 'businesslistings' . '&amp;' . NV_OP_VARIABLE . '=stocks';
        $active_province['link__qhxddt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['quyhoach'];
        $active_province['link__plans_overall'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['planoverall'] . '/' . Url::getKHTTLCNT();
        $active_province['link__mstnht'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['prequalification'] . '/' . Url::getNT();
        $active_province['link__moiquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . Url::getMQTNT() . '/' . Url::getNT1();
        $active_province['link__ketquasotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['resultpq'] . '/' . Url::getNT();
        $active_province['link__ketquamoiquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . Url::getKQMQTNT() . '/' . Url::getNT1();
        $active_province['link__open'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['open'];
        $active_province['link__ketquamosotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['viewopenpq'] . '/' . Url::getNT();
        $active_province['link__ketquamoquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . Url::getKQMHSQTNT() . '/' . Url::getNT(); 
        $active_province['link__hanghoa'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['hanghoa']; 
        $active_province['link__cbda'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=project'; 
        $active_province['link__moisotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['prequalification'] . '/' . Url::getNDT(); 
        $active_province['link__kqst_project'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['resultpq'] . '/' . Url::getNDT(); 
        $active_province['link__vbdt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $laws_module_name. '/search&amp;area=5'; 
        $active_province['link__moidautu'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' .$_module_info['alias']['view'] . '/' . Url::getTBMDT(); 

        if (!empty($array_op[1])) {
            $active_province['title'] = $active_province[0]['title'];
            if (!empty($active_province[0]['images'])) {
                $active_province['thumb'] = $active_province[0]['images'];
            } else {
                $active_province['thumb'] = NV_BASE_SITEURL . NV_UPLOADS_DIR . '/province/tinhthanh.jpg';
            }
            $active_province['description'] = !empty($active_province[0]['description']) ? $active_province[0]['description'] : $nv_Lang->getModule('description_province');
            $provinceid = $province_id = $active_province[0]['id'];
            $province_alias = $active_province[0]['alias'];
            $active_province['link_devprojects'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=devprojects&idprovince=' . $province_id . '&home_page=1';
            $active_province['link_listplan'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['plans'] . '/' . Url::getKHLCNT() . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_alias . '&home_page=1';
            $active_province['link_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['view'] . '/' . Url::getTBMT() . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_alias . '&home_page=1';
            $active_province['link_listresult'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['result'] . '/' . Url::getKQLCNT() . '&idprovincekq=' . $province_id . '&home_page=1'; 
            $active_province['link_planndt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['plans'] . '/' . Url::getKHLCNDT() . '&idprovince=' . $province_id . '&home_page=1';
            $active_province['link_kqlcndt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['result'] . '/' . Url::getKQLCNDT() . '&idprovincekq=' . $province_id . '&home_page=1';
            $active_province['link_nhathau'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=businesslistings/listlocation/T-' . $province_alias . '-' . $province_id;
            $active_province['link_ntvipham'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['nha-thau-vi-pham'] . '&home_page=1';
            $active_province['link_bmt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=procuring-entity&province=' . $province_id . '&home_page=1';
            $active_province['link_las'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=las/' . strtolower($array_op[1]) . '&home_page=1';
            $active_province['link_tcxd'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=to-chuc/listlocation/' . $province_alias . '&home_page=1';
            $active_province['link_vilas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vilas/tt-' . $province_alias . '&home_page=1';
            $active_province['link_vicas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vicas/tt-' . $province_alias . '&home_page=1';
            $active_province['link_vias'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vias/tt-' . $province_alias . '&home_page=1';
            $active_province['link_vilas_med'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vilas-med/tt-' . $province_alias . '&home_page=1';
            $active_province['link_vipas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=vipas/tt-' . $province_alias . '&home_page=1';
            $active_province['link_viras'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=viras/tt-' . $province_alias . '&home_page=1';
            $active_province['link_dau_gia'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia&keyword_id_province=' . $province_id . '&home_page=1';
            $active_province['link__dau_gia_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '?is_advance=1&keyword_id_province=' . $province_id . '&home_page=1';
            $active_province['link_dau_gia_select'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia' . '&' . NV_OP_VARIABLE . '=bidorganization&keyword_id_province=' . $province_id . '&home_page=1';
            $active_province['link__dau_gia_select_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/bidorganization?is_advance=1&keyword_id_province=' . $province_id . '&home_page=1';
            $active_province['link_dau_gia_bidder'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia' . '&' . NV_OP_VARIABLE . '=organization&province=' . $province_id . '&home_page=1';
            $active_province['link_dau_gia_dgv'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia' . '&' . NV_OP_VARIABLE . '=' . $site_mods['dau-gia']['alias']['auctioneer'] . '&province=' . $province_id . '&home_page=1';
            $active_province['link_dau_gia_department'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=dau-gia' . '&' . NV_OP_VARIABLE . '=department&province=' . $province_id . '&home_page=1';
            $active_province['link_stocks'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . 'businesslistings' . '&' . NV_OP_VARIABLE . '=stocks&location=' . $province_id . '&home_page=1';
            $active_province['link_qhxddt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['quyhoach'] . '/' . $province_alias . '&home_page=1';
            $active_province['link_plans_overall'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['planoverall'] . '/' . Url::getKHTTLCNT() . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_alias . '&home_page=1';
            $active_province['link_mstnht'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['prequalification'] . '/' . Url::getNT() . '&home_page=1';
            $active_province['link_moiquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . Url::getMQTNT() . '/' . Url::getNT1() . '&home_page=1';
            $active_province['link_ketquasotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['resultpq'] . '/' . Url::getNT() . '&home_page=1';
            $active_province['link_ketquamoiquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . Url::getKQMQTNT() . '/' . Url::getNT1() . '&home_page=1';
            $active_province['link_open'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['open'] . '&home_page=1';
            $active_province['link_ketquamosotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['viewopenpq'] . '/' . Url::getNT() . '&home_page=1';
            $active_province['link_ketquamoquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . Url::getKQMHSQTNT() . '/' . Url::getNT() . '&home_page=1'; 
            $active_province['link_hanghoa'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['hanghoa'] . '&home_page=1'; 
            $active_province['link_cbda'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=project&home_page=1'; 
            $active_province['link_moisotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['prequalification'] . '/' . Url::getNDT() . '&home_page=1'; 
            $active_province['link_kqst_project'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' . $_module_info['alias']['resultpq'] . '/' . Url::getNDT() . '&home_page=1';
            $active_province['link_vbdt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $laws_module_name . '/search&area=5&home_page=1'; 
            $active_province['link_moidautu'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $_module_name . '&' . NV_OP_VARIABLE . '=' .$_module_info['alias']['view'] . '/' . Url::getTBMDT() . '&home_page=1'; 
        
            $active_province['link__devprojects'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=devprojects/&amp;idprovince=' . $province_id;
            $active_province['link__listplan'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['plans'] . '/' . Url::getKHLCNT() . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_alias;
            $active_province['link__detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['view'] . '/' . Url::getTBMT() . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_alias;
            $active_province['link__listresult'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['result'] . '/' . Url::getKQLCNT() . '/' . $_module_info['alias']['tinh-thanh'] . '/' . $province_alias;
            $active_province['link__planndt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['plans'] . '/' . Url::getKHLCNDT() . '&amp;idprovince=' . $province_id;
            $active_province['link__kqlcndt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['result'] . '/' . Url::getKQLCNDT() . '&amp;idprovincekq=' . $province_id;
            $active_province['link__nhathau'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings/listlocation/T-' . $province_alias . '-' . $province_id;
            $active_province['link__ntvipham'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['nha-thau-vi-pham'];
            $active_province['link__bmt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=procuring-entity&amp;province=' . $province_id;
            $active_province['link__las'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=las/' . strtolower($array_op[1]);
            $active_province['link__tcxd'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=to-chuc/listlocation/' . $province_alias;
            $active_province['link__vilas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vilas/tt-' . $province_alias;
            $active_province['link__vicas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vicas/tt-' . $province_alias;
            $active_province['link__vias'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vias/tt-' . $province_alias;
            $active_province['link__vilas_med'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vilas-med/tt-' . $province_alias;
            $active_province['link__vipas'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=vipas/tt-' . $province_alias;
            $active_province['link__viras'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=viras/tt-' . $province_alias;
            $active_province['link__dau_gia'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia&amp;keyword_id_province=' . $province_id;
            $active_province['link__dau_gia_select'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia' . '&amp;' . NV_OP_VARIABLE . '=bidorganization&amp;keyword_id_province=' . $province_id;
            $active_province['link__dau_gia_bidder'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia' . '&amp;' . NV_OP_VARIABLE . '=organization&amp;province=' . $province_id;
            $active_province['link__dau_gia_bidder_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/organization?is_advance=1&province=' . $province_id;
            $active_province['link__dau_gia_dgv'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia' . '&amp;' . NV_OP_VARIABLE . '=' . $site_mods['dau-gia']['alias']['auctioneer'] . '&amp;province=' . $province_id;
            $active_province['link__dau_gia_dgv_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/' . $site_mods['dau-gia']['alias']['auctioneer'] . '?is_advance=1&province=' . $province_id;
            $active_province['link__dau_gia_department'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dau-gia' . '&amp;' . NV_OP_VARIABLE . '=department&amp;province=' . $province_id;
            $active_province['link__dau_gia_department_viewmore'] = DAUGIA_DOMAIN . '/' . NV_LANG_DATA . '/department/?is_advance=1&province=' . $province_id;
            $active_province['link__stocks'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . 'businesslistings' . '&amp;' . NV_OP_VARIABLE . '=stocks&amp;location=' . $province_id;
            $active_province['link__qhxddt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['quyhoach'] . '/' . $province_alias;
            $active_province['link__plans_overall'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['planoverall'] . '/' . Url::getKHTTLCNT() . '/' . $nv_Lang->getModule('linkprovince') . '/' . $province_alias;
            $active_province['link__mstnht'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['prequalification'] . '/' . Url::getNT();
            $active_province['link__moiquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . Url::getMQTNT() . '/' . Url::getNT1();
            $active_province['link__ketquasotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['resultpq'] . '/' . Url::getNT();
            $active_province['link__ketquamoiquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . Url::getKQMQTNT() . '/' . Url::getNT1();
            $active_province['link__open'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['open'];
            $active_province['link__ketquamosotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['viewopenpq'] . '/' . Url::getNT();
            $active_province['link__ketquamoquantam'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . Url::getKQMHSQTNT() . '/' . Url::getNT(); 
            $active_province['link__hanghoa'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['hanghoa']; 
            $active_province['link__cbda'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=project'; 
            $active_province['link__moisotuyen'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['prequalification'] . '/' . Url::getNDT(); 
            $active_province['link__kqst_project'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' . $_module_info['alias']['resultpq'] . '/' . Url::getNDT();
            $active_province['link__vbdt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $laws_module_name . '/search&amp;area=5'; 
            $active_province['link__moidautu'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $_module_name . '&amp;' . NV_OP_VARIABLE . '=' .$_module_info['alias']['view'] . '/' . Url::getTBMDT(); 

            $class_curent = "";
            $class = "apexcharts-x";
            if (!empty($province_data[$provinceid]['values'])) {
                $count_value = count($province_data[$provinceid]['values']);
                if ($count_value < 5) {
                    $width_statistics = 350;
                    $class = "apexcharts-x";
                } else {
                    $class = "apexcharts-x-11";
                    $width_statistics = 470;
                }
            }
            $statistics_labels = isset($province_data[$provinceid]['labels']) ? $province_data[$provinceid]['labels'] : [];
            $statistics_colors = isset($province_data[$provinceid]['colors']) ? $province_data[$provinceid]['colors'] : [];
            $statistics_values = isset($province_data[$provinceid]['values']) ? $province_data[$provinceid]['values'] : [];
            if (!empty($statistics_values)) {
                $xtpl->assign('STATISTICS_PROVINCE', array(
                    'id' => $provinceid,
                    'title' => $active_province['title'],
                    'width_statistics' => $width_statistics,
                    'labels' => json_encode($statistics_labels),
                    'colors' => json_encode($statistics_colors),
                    'values' => json_encode($statistics_values),
                    'class' => $class,
                    'class_curent' => $class_curent
                ));
                $xtpl->parse('main.province.plan_pie');
                $xtpl->parse('main.province.js_plan');
            }
        } else {
            $province_titles = [
                101 => $nv_Lang->getModule('ha_noi'),
                501 => $nv_Lang->getModule('da_nang'),
                701 => $nv_Lang->getModule('tphcm')
            ];
            $province_ids = [
                101,
                501,
                701
            ];
            foreach ($province_ids as $provinceid) {
                $class_curent = "";
                if ($provinceid == 701) {
                    $class_curent = "right";
                }
                if ($provinceid == 501) {
                    $class_curent = "bottom";
                }
                $class = "apexcharts-x";
                if (!empty($province_data[$provinceid]['values'])) {
                    $count_value = count($province_data[$provinceid]['values']);
                    if ($count_value < 5) {
                        $width_statistics = 350;
                        $class = "apexcharts-x";
                    } else {
                        $class = "apexcharts-x-11";
                        $width_statistics = 470;
                    }
                }
                if ($provinceid == 701 || $provinceid == 501) {
                    if (!empty($province_data[$provinceid]['values'])) {
                        $count_value = count($province_data[$provinceid]['values']);
                        if ($count_value < 5) {
                            $width_statistics = 280;
                        } else if ($count_value < 10) {
                            $width_statistics = 300;
                            $class = "apexcharts-x1-10";
                        } else if ($count_value > 10) {
                            $class = "apexcharts-x1-11";
                            $width_statistics = 320;
                        }
                    }
                }
                $statistics_labels = isset($province_data[$provinceid]['labels']) ? $province_data[$provinceid]['labels'] : [];
                $statistics_colors = isset($province_data[$provinceid]['colors']) ? $province_data[$provinceid]['colors'] : [];
                $statistics_values = isset($province_data[$provinceid]['values']) ? $province_data[$provinceid]['values'] : [];
                if (!empty($statistics_values)) {
                    // Gắn dữ liệu vào mẫu
                    $xtpl->assign('STATISTICS_PROVINCE', array(
                        'id' => $provinceid,
                        'title' => $province_titles[$provinceid],
                        'width_statistics' => $width_statistics,
                        'labels' => json_encode($statistics_labels),
                        'colors' => json_encode($statistics_colors),
                        'values' => json_encode($statistics_values),
                        'class' => $class,
                        'class_curent' => $class_curent
                    ));
                    $xtpl->parse('main.province.plan_pie');
                    $xtpl->parse('main.province.js_plan');
                }
            }
        }
        $class_total_dadtpt = '';
        if($array_province['total_dadtpt'] == 0) {
            $class_total_dadtpt = 'hidden';
        }
        $xtpl->assign('class_total_dadtpt', $class_total_dadtpt);

        $class_total_plans = '';
        if($array_province['total_plans'] == 0) {
            $class_total_plans = 'hidden';
        }
        $xtpl->assign('class_total_plans', $class_total_plans);

        $class_total_kqlcnt = '';
        if($array_province['total_kqlcnt'] == 0) {
            $class_total_kqlcnt = 'hidden';
        }
        $xtpl->assign('class_total_kqlcnt', $class_total_kqlcnt);

        $class_total_nhathau = '';
        if($array_province['total_nhathau'] == 0) {
            $class_total_nhathau = 'hidden';
        }
        $xtpl->assign('class_total_nhathau', $class_total_nhathau);

        $class_total_bmt = '';
        if($array_province['total_bmt'] == 0) {
            $class_total_bmt = 'hidden';
        }
        $xtpl->assign('class_total_bmt', $class_total_bmt);

        $class_total_las = '';
        if($array_province['total_las'] == 0) {
            $class_total_las = 'hidden';
        }
        $xtpl->assign('class_total_las', $class_total_las);

        $class_total_khlcndt = '';
        if($array_province['total_khlcndt'] == 0) {
            $class_total_khlcndt = 'hidden';
        }
        $xtpl->assign('class_total_khlcndt', $class_total_khlcndt);
        
        $class_total_kqlcndt = '';
        if($array_province['total_kqlcndt'] == 0) {
            $class_total_kqlcndt = 'hidden';
        }
        $xtpl->assign('class_total_kqlcndt', $class_total_kqlcndt);

        $class_total_tochuc = '';
        if($array_province['total_tochuc'] == 0) {
            $class_total_tochuc = 'hidden';
        }
        $xtpl->assign('class_total_tochuc', $class_total_tochuc);

        $class_total_project = '';
        if($array_province['total_project'] == 0) {
            $class_total_project = 'hidden';
        }
        $xtpl->assign('class_total_project', $class_total_project);

        $class_total_contract = '';
        if($array_province['total_contract'] == 0) {
            $class_total_contract = 'hidden';
        }
        $xtpl->assign('class_total_contract', $class_total_contract);

        $class_total_vilas = '';
        if($array_province['total_vilas'] == 0) {
            $class_total_vilas = 'hidden';
        }
        $xtpl->assign('class_total_vilas', $class_total_vilas);

        $class_total_vicas = '';
        if($array_province['total_vicas'] == 0) {
            $class_total_vicas = 'hidden';
        }
        $xtpl->assign('class_total_vicas', $class_total_vicas);

        $class_total_vias = '';
        if($array_province['total_vias'] == 0) {
            $class_total_vias = 'hidden';
        }
        $xtpl->assign('class_total_vias', $class_total_vias);

        $class_total_vilas_med = '';
        if($array_province['total_vilas_med'] == 0) {
            $class_total_vilas_med = 'hidden';
        }
        $xtpl->assign('class_total_vilas_med', $class_total_vilas_med);

        $class_total_dau_gia = '';
        if($array_province['total_dau_gia'] == 0) {
            $class_total_dau_gia = 'hidden';
        }
        $xtpl->assign('class_total_dau_gia', $class_total_dau_gia);

        $class_total_dau_gia_select = '';
        if($array_province['total_dau_gia_select'] == 0) {
            $class_total_dau_gia_select = 'hidden';
        }
        $xtpl->assign('class_total_dau_gia_select', $class_total_dau_gia_select);

        $class_total_dau_gia_bidder = '';
        if($array_province['total_dau_gia_bidder'] == 0) {
            $class_total_dau_gia_bidder = 'hidden';
        }
        $xtpl->assign('class_total_dau_gia_bidder', $class_total_dau_gia_bidder);

        $class_total_dau_gia_dgv = '';
        if($array_province['total_dau_gia_dgv'] == 0) {
            $class_total_dau_gia_dgv = 'hidden';
        }
        $xtpl->assign('class_total_dau_gia_dgv', $class_total_dau_gia_dgv);

        $class_total_dau_gia_deparment = '';
        if($array_province['total_dau_gia_deparment'] == 0) {
            $class_total_dau_gia_deparment = 'hidden';
        }
        $xtpl->assign('class_total_dau_gia_deparment', $class_total_dau_gia_deparment);

        $class_total_stocks = '';
        if($array_province['total_stocks'] == 0) {
            $class_total_stocks = 'hidden';
        }
        $xtpl->assign('class_total_stocks', $class_total_stocks);

        $class_total_plans_overall = '';
        if($array_province['total_plans_overall'] == 0) {
            $class_total_plans_overall = 'hidden';
        }
        $xtpl->assign('class_total_plans_overall', $class_total_plans_overall);

        $class_total_mstnht = '';
        if($array_province['total_mstnht'] == 0) {
            $class_total_mstnht = 'hidden';
        }
        $xtpl->assign('class_total_mstnht', $class_total_mstnht);

        $class_total_moiquantam = '';
        if($array_province['total_moiquantam'] == 0) {
            $class_total_moiquantam = 'hidden';
        }
        $xtpl->assign('class_total_moiquantam', $class_total_moiquantam);

        $class_total_ketquasotuyen = '';
        if($array_province['total_ketquasotuyen'] == 0) {
            $class_total_ketquasotuyen = 'hidden';
        }
        $xtpl->assign('class_total_ketquasotuyen', $class_total_ketquasotuyen);

        $class_total_ketquamoiquantam = '';
        if($array_province['total_ketquamoiquantam'] == 0) {
            $class_total_ketquamoiquantam = 'hidden';
        }
        $xtpl->assign('class_total_ketquamoiquantam', $class_total_ketquamoiquantam);

        $class_total_open = '';
        if($array_province['total_open'] == 0) {
            $class_total_open = 'hidden';
        }
        $xtpl->assign('class_total_open', $class_total_open);

        $class_total_ketquamosotuyen = '';
        if($array_province['total_ketquamosotuyen'] == 0) {
            $class_total_ketquamosotuyen = 'hidden';
        }
        $xtpl->assign('class_total_ketquamosotuyen', $class_total_ketquamosotuyen);

        $class_total_ketquamoquantam = '';
        if($array_province['total_ketquamoquantam'] == 0) {
            $class_total_ketquamoquantam = 'hidden';
        }
        $xtpl->assign('class_total_ketquamoquantam', $class_total_ketquamoquantam);

        $class_total_hanghoa = '';
        if($array_province['total_hanghoa'] == 0) {
            $class_total_hanghoa = 'hidden';
        }
        $xtpl->assign('class_total_hanghoa', $class_total_hanghoa);

        $class_total_ntvp = '';
        if($array_province['total_ntvp'] == 0) {
            $class_total_ntvp = 'hidden';
        }
        $xtpl->assign('class_total_ntvp', $class_total_ntvp);

        $class_total_cbda = '';
        if($array_province['total_cbda'] == 0) {
            $class_total_cbda = 'hidden';
        }
        $xtpl->assign('class_total_cbda', $class_total_cbda);

        $class_total_moisotuyen = '';
        if($array_province['total_moisotuyen'] == 0) {
            $class_total_moisotuyen = 'hidden';
        }
        $xtpl->assign('class_total_moisotuyen', $class_total_moisotuyen);

        $class_total_kqst_project = '';
        if($array_province['total_kqst_project'] == 0) {
            $class_total_kqst_project = 'hidden';
        }
        $xtpl->assign('class_total_kqst_project', $class_total_kqst_project);

        $class_total_vbdt = '';
        if($array_province['total_vbdt'] == 0) {
            $class_total_vbdt = 'hidden';
        }
        $xtpl->assign('class_total_vbdt', $class_total_vbdt);
        $class_total_vipas = '';
        if($array_province['total_vipas'] == 0) {
            $class_total_vipas = 'hidden';
        }
        $xtpl->assign('class_total_vipas', $class_total_vipas);

        $class_total_viras = '';
        if($array_province['total_viras'] == 0) {
            $class_total_viras = 'hidden';
        }
        $xtpl->assign('class_total_viras', $class_total_viras);

        $class_total_moidautu = '';
        if($array_province['total_moidautu'] == 0) {
            $class_total_moidautu = 'hidden';
        }
        $xtpl->assign('class_total_moidautu', $class_total_moidautu);

        $active_province['total_province'] = number_format($array_province['total_province'], 0, ",", ".");
        $active_province['total_dadtpt'] = number_format($array_province['total_dadtpt'], 0, ",", ".");
        $active_province['total_plans'] = number_format($array_province['total_plans'], 0, ",", ".");
        $active_province['total_kqlcnt'] = number_format($array_province['total_kqlcnt'], 0, ",", ".");
        $active_province['total_nhathau'] = number_format($array_province['total_nhathau'], 0, ",", ".");
        $active_province['total_bmt'] = number_format($array_province['total_bmt'], 0, ",", ".");
        $active_province['total_las'] = number_format($array_province['total_las'], 0, ",", ".");
        $active_province['total_tochuc'] = number_format($array_province['total_tochuc'], 0, ",", ".");
        $active_province['total_project'] = number_format($array_province['total_project'], 0, ",", ".");
        $active_province['total_contract'] = number_format($array_province['total_contract'], 0, ",", ".");
        $active_province['total_vilas'] = number_format($array_province['total_vilas'], 0, ",", ".");
        $active_province['total_vicas'] = number_format($array_province['total_vicas'], 0, ",", ".");
        $active_province['total_vias'] = number_format($array_province['total_vias'], 0, ",", ".");
        $active_province['total_vilas_med'] = number_format($array_province['total_vilas_med'], 0, ",", ".");
        $active_province['total_dau_gia'] = number_format($array_province['total_dau_gia'], 0, ",", ".");
        $active_province['total_dau_gia_select'] = number_format($array_province['total_dau_gia_select'], 0, ",", ".");
        $active_province['total_dau_gia_bidder'] = number_format($array_province['total_dau_gia_bidder'], 0, ",", ".");
        $active_province['total_dau_gia_dgv'] = number_format($array_province['total_dau_gia_dgv'], 0, ",", ".");
        $active_province['total_dau_gia_deparment'] = number_format($array_province['total_dau_gia_deparment'], 0, ",", ".");
        $active_province['total_stocks'] = number_format($array_province['total_stocks'], 0, ",", ".");
        $active_province['total_plans_overall'] = number_format($array_province['total_plans_overall'], 0, ",", ".");
        $active_province['total_mstnht'] = number_format($array_province['total_mstnht'], 0, ",", ".");
        $active_province['total_moiquantam'] = number_format($array_province['total_moiquantam'], 0, ",", ".");
        $active_province['total_ketquasotuyen'] = number_format($array_province['total_ketquasotuyen'], 0, ",", ".");
        $active_province['total_ketquamoiquantam'] = number_format($array_province['total_ketquamoiquantam'], 0, ",", ".");
        $active_province['total_open'] = number_format($array_province['total_open'], 0, ",", ".");
        $active_province['total_ketquamosotuyen'] = number_format($array_province['total_ketquamosotuyen'], 0, ",", ".");
        $active_province['total_ketquamoquantam'] = number_format($array_province['total_ketquamoquantam'], 0, ",", ".");
        $active_province['total_hanghoa'] = number_format($array_province['total_hanghoa'], 0, ",", ".");
        $active_province['total_ntvp'] = number_format($array_province['total_ntvp'], 0, ",", ".");
        $active_province['total_cbda'] = number_format($array_province['total_cbda'], 0, ",", ".");
        $active_province['total_khlcndt'] = number_format($array_province['total_khlcndt'], 0, ",", ".");
        $active_province['total_moisotuyen'] = number_format($array_province['total_moisotuyen'], 0, ",", ".");
        $active_province['total_kqst_project'] = number_format($array_province['total_kqst_project'], 0, ",", ".");
        $active_province['total_kqlcndt'] = number_format($array_province['total_kqlcndt'], 0, ",", ".");
        $active_province['total_vbdt'] = number_format($array_province['total_vbdt'], 0, ",", ".");
        $active_province['total_vipas'] = number_format($array_province['total_vipas'], 0, ",", ".");
        $active_province['total_viras'] = number_format($array_province['total_viras'], 0, ",", ".");
        $active_province['total_moidautu'] = number_format($array_province['total_moidautu'], 0, ",", ".");
        
        $date = NV_CURRENTTIME; // Current time
        $prev_date = strtotime('yesterday', $date);
        $note_province = sprintf($nv_Lang->getModule('note_province'), date("d-m-Y", $prev_date));
        $xtpl->assign('NOTE_PROVINCE', $note_province);
        $xtpl->assign('CLASS_MOBILE', $class_mobile);
        $xtpl->assign('ID_PROVINCE', $province_id);
        $xtpl->assign('PROVINCETYPE', $active_province);
        if (!empty($active_province['thumb'])) {
            $xtpl->parse('main.province.type.images');
        }
        if (!empty($active_province['description'])) {
            $xtpl->parse('main.province.type.description');
        }
        $xtpl->parse('main.province.type');
        $xtpl->parse('main.province.count');
        if (empty($array_data)) {
            $xtpl->parse('main.province.empty');
        } else {
    
            foreach ($array_data as $view) {
                $view['is_violated'] && ViolateMsg::addMsgTbmt($view);
                
                $xtpl->assign('VIEW', $view);
                if (defined('NV_IS_MODADMIN')) {
                    $xtpl->parse('main.province.view.loop.admin_link');
                }
    
                if (!empty($view['link_solicitor'])) {
                    if (!empty($view['solicitor_code'])) {
                        $xtpl->parse('main.province.view.loop.link_solicitor.solicitor_code');
                    }
                    $xtpl->parse('main.province.view.loop.link_solicitor');
                } else {
                    if (!empty($view['solicitor_code'])) {
                        $xtpl->parse('main.province.view.loop.no_link_solicitor.solicitor_code');
                    }
                    $xtpl->parse('main.province.view.loop.no_link_solicitor');
                }
                if (!empty($view['is_violated'])) {
                    $xtpl->parse('main.province.view.loop.violate_warning');
                }
                $xtpl->parse('main.province.view.loop');
            }
    
            if (defined('NV_IS_MODADMIN')) {
                $xtpl->parse('main.province.view.admin_link_col');
            }
    
            if (!empty($generate_page)) {
                $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
                $xtpl->parse('main.province.view.generate_page');
            }
        }
        $xtpl->parse('main.province.view');
        $xtpl->parse('main.province');
        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    global $nv_Cache, $site_mods;
    $content = nv_bidding_tinhthanh($block_config);
}
