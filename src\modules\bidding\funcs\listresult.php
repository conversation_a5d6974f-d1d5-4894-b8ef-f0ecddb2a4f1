<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 19 Jan 2018 03:31:18 GMT
 */
if (!defined('NV_IS_MOD_BIDDING')) {
    die('Stop!!!');
}

use NukeViet\Dauthau\Url;
use NukeViet\Dauthau\Share;

$array_data = $error = array();
$arr_unexist_key = array();

$type_search = $nv_Request->get_int('type_search', 'post,get', 0); // 1. Lựa chọn nhà thầu; 2. Lựa chọn nhà đầu tư
$type_info = $nv_Request->get_int('type_info', 'post,get', 1);
$type_info2 = $nv_Request->get_int('type_info2', 'post,get', 1);
$static = $nv_Request->get_int('static', 'post,get', 0);
$vip_static = $nv_Request->get_int('vip_static', 'post,get', 0);
$type_kqlcnt = $nv_Request->get_int('type_kqlcnt', 'get', 0); // Trạng thái liên kết với KHLCNT
$bidfieid = $nv_Request->get_typed_array('bidfieid', 'post,get', 'string', array());
$is_advance = $nv_Request->get_int('is_advance', 'get', 0); // Tìm kiếm nâng cao
$show_vip = true; // Hiện nhà thầu nào là khách
$listresult_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'];
$base_result_url = URL_DTNET_SITE . NV_LANG_DATA . '/' . $dtnet_module_bids . '/' . $dtnet_module_bids_result . '/?type_info=5&amp;is_advance=0&amp;post_type=dang-cong-khai';
if (!defined('NV_IS_ADMIN') || $type_search == 2) {
    $static = $vip_static = 0;
    $show_vip = false;
}
$page_title = $nv_Lang->getModule('kq_lcnt'); 
// Danh sách kết quả lựa chọn nhà thầu và nhà đầu tư theo tab
if (empty($array_op) && $listresult_page) {
    $page_title = $nv_Lang->getModule('result_title');
    $description = $nv_Lang->getModule('description_result');
    $base_url = $listresult_url;
    $global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $other_lang_module_info['alias']['result'];
    $canonicalUrl = getCanonicalUrl($listresult_url);
    $array_mod_title[] = array(
        'title' => $nv_Lang->getModule('result_title'),
        'link' => nv_url_rewrite($listresult_url, true)
    );
    // Danh sách KQLCNT
    $arr_tbmt = $arr_solicitor_id = $list_solicitor = $arr_bidder = $rids = [];
    $array_data = handle_array_data(1);
    $array_data_nt = handle_array_result($array_data, 1, false);

    // Danh sách KQLCNDT
    $arr_tbmt = $arr_solicitor_id = $list_solicitor = $arr_bidder = $rids = [];
    $array_data = handle_array_data(2);
    $array_data_ndt = handle_array_result($array_data, 2, false);

    $link_more_nt = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT();
    $link_more_ndt = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNDT();
    $contents = nv_theme_bidding_general_result($array_data_nt, $link_more_nt, $array_data_ndt, $link_more_ndt);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}
// Xử lý URL tỉnh thành cho KQLCNT
if (isset($array_op[3]) && isset($array_op[2]) && ($array_op[2] == 'tinh-thanh' || $array_op[2] == 'tenderlistbylocation')) {
    if (preg_match('/^([A-Za-z0-9\-]+)$/', $array_op[3], $m)) {
        $_temp = null;
        if ($m[0] == 'Chua-phan-loai') {
            $_temp = $_idprovince[] = 0;
        } else if ($m[0] == change_alias($nv_Lang->getModule('alias_vn_out_territory'))) {
            $_temp = $_idprovince[] = 825;
        } else if ($m[0] == change_alias($nv_Lang->getModule('alias_nationwide'))) {
            $_temp = $_idprovince[] = 824;
        } else {
            $_temp = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE alias = ' . $db->quote($m[0]))->fetchColumn();
            if (!empty($_temp)) {
                $_idprovince[] = $_temp;
            } else {
                nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT(), true);
            }
        }

        $is_advance = 1;
        $current_op = $module_info['alias']['result'] . '/' . Url::getKQLCNT() . '/' . $module_info['alias']['tinh-thanh'] . '/' . $array_op[3];
        $current_other_op = $other_lang_module_info['alias']['result'] . '/' . Url::URL_KQLCNT[$other_lang] . '/' . $other_lang_module_info['alias']['tinh-thanh'] . '/' . $array_op[3];

        // Xử lý page title cho tỉnh thành
        if (!empty($_temp)) {
            $arr_title_province = [
                815, // Tp Cần Thơ
                501, // Đà Nẵng
                101, // Hà Nội
                103, // Hải Phòng
                701  // HCM
            ];

            if ($_temp == 825) {
                $province_title = $nv_Lang->getModule('vn_out_territory');
                $pro = '';
            } else if ($_temp == 824) {
                $province_title = $nv_Lang->getModule('nationwide');
                $pro = '';
            } else if ($_temp == 0) {
                $province_title = 'Chưa phân loại';
                $pro = '';
            } else {
                $province_info = $db->query('SELECT title FROM ' . NV_PREFIXLANG . '_location_province WHERE id = ' . $_temp)->fetch();
                $province_title = !empty($province_info) ? str_replace('TP.', '', $province_info['title']) : '';

                // Xác định tiền tố Thành phố hoặc Tỉnh
                if (in_array($_temp, $arr_title_province)) {
                    $pro = $nv_Lang->getModule('idprovince_i'); // "Thành phố "
                } else {
                    $pro = $nv_Lang->getModule('idprovince_ii'); // "Tỉnh "
                }
            }

            if (!empty($province_title)) {
                $full_province_title = $pro . $province_title;
                $page_title = $nv_Lang->getModule('kq_lcnt') . ' ' . $full_province_title;
                $description = $nv_Lang->getModule('description_result_awarded_contractor') . ' ' . $full_province_title;
            }
        }
    }
}

if ($array_op[1] == Url::getKQLCNDT() || $type_search == 2) {
    if ($type_search == 1) {
        $type_search = 1;
        $current_op = $module_info['alias']['result'] . '/' . Url::getKQLCNT();
        $current_other_op = $other_lang_module_info['alias']['result'] . '/' . Url::URL_KQLCNT[$other_lang];
    } else {
        $type_search = 2;
        $current_op = $module_info['alias']['result'] . '/' . Url::getKQLCNDT();
        $current_other_op = $other_lang_module_info['alias']['result'] . '/' . Url::URL_KQLCNDT[$other_lang];
    }
} else {
    $type_search = 1;
    if (!isset($array_op[3])) {
        $current_op = $module_info['alias']['result'] . '/' . Url::getKQLCNT();
        $current_other_op = $other_lang_module_info['alias']['result'] . '/' . Url::URL_KQLCNT[$other_lang];
    }
    if (isset($array_op[2]) && !isset($array_op[3])) {
        switch ($array_op[2]) {
            case 'linked':
                $type_kqlcnt = 1;
                $is_advance = 1;
                $current_op = $module_info['alias']['result'] . '/' . Url::getKQLCNT() . '/linked';
                $current_other_op = $other_lang_module_info['alias']['result'] . '/' . Url::URL_KQLCNT[$other_lang] . '/linked';
                break;
            case 'unlinked':
                $type_kqlcnt = 2;
                $is_advance = 1;
                $current_op = $module_info['alias']['result'] . '/' . Url::getKQLCNT() . '/unlinked';
                $current_other_op = $other_lang_module_info['alias']['result'] . '/' . Url::URL_KQLCNT[$other_lang] . '/unlinked';
                break;
        }
    }
}
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $current_op;
// Chỉ set page_title và description mặc định nếu chưa được set ở phần tỉnh thành
if (!isset($page_title)) {
    $page_title = $type_search == 2 ? $nv_Lang->getModule('ket_qua_lcndt') : $nv_Lang->getModule('kq_lcnt');
}
if (!isset($description)) {
    $description = $type_search == 2 ? $nv_Lang->getModule('description_result_awarded_investor') : $nv_Lang->getModule('description_result_awarded_contractor');
}
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('result_title'),
    'link' => nv_url_rewrite($listresult_url, true)
);
$array_mod_title[] = [
    'title' => $type_search == 2 ? $nv_Lang->getModule('ket_qua_lcndt') : $nv_Lang->getModule('kq_lcnt'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . ($type_search == 2 ? Url::getKQLCNDT() : Url::getKQLCNT()), true)
];
$l = $nv_Request->get_int('l', 'post', -1);
$q = $nv_Request->get_title('q', 'post,get');
$code = $nv_Request->get_title('code', 'post,get', '');
$key_search2 = $nv_Request->get_title('q2', 'post,get');
$search_one_key = $nv_Request->get_int('search_one_key', 'post,get', 0); // Một trong các từ khóa bổ sung là bắt buộc

$catressult = $nv_Request->get_int('catressult', 'get', 0);
$without_key = $nv_Request->get_title('without_key', 'post,get'); // Từ khóa loại trừ

$solicitor_id = $nv_Request->get_int('solicitor_id', 'get', 0);
$sfrom = nv_substr($nv_Request->get_title('sfrom', 'get', ''), 0, 10);
$sto = nv_substr($nv_Request->get_title('sto', 'get', ''), 0, 10);

$price_plan_from = $nv_Request->get_string('price_plan_from', 'get', ''); // Giá gói thầu trong KHLCNT
$price_plan_from = floatval(str_replace(',', '', $price_plan_from));
$price_plan_to = $nv_Request->get_string('price_plan_to', 'get', '');
$price_plan_to = floatval(str_replace(',', '', $price_plan_to));
$win_price_from = $nv_Request->get_string('win_price_from', 'get', '');
$win_price_from = floatval(str_replace(',', '', $win_price_from));
$win_price_to = $nv_Request->get_string('win_price_to', 'get', '');
$win_price_to = floatval(str_replace(',', '', $win_price_to));
$type_bid = $nv_Request->get_int('type_bid', 'get', 0);
$type_choose_id = $nv_Request->get_int('type_choose_id', 'get', 0);
$search_type_content = $nv_Request->get_int('search_type_content', 'post,get', 0); // 0. Tương đối; 1. Tuyệt đối
$par_search = $nv_Request->get_int('par_search', 'post,get', 0);
$goods_2 = $nv_Request->get_int('goods_2', 'post,get', 0);
$goods = $nv_Request->get_int('goods', 'post,get', 0);
if ($goods_2) {
    $goods = $goods_2;
}
$_idprovince = $nv_Request->get_array('idprovincekq', 'get,post', []); // Tỉnh thành
foreach ($_idprovince as $key => $val) {
    if ($val === '' || $val === '-1') {
        unset($_idprovince[$key]);
    }
}
$search_kind = $nv_Request->get_int('searchkind', 'post,get', 0);
$max_vaule_price = Share::MAX_VALUE_PRICE;
$key_search = $q;
$error_money = ($price_plan_from > $max_vaule_price || $price_plan_to > $max_vaule_price || $win_price_from > $max_vaule_price || $win_price_to > $max_vaule_price) ? $nv_Lang->getModule('error_money_from') : '';
if ($price_plan_from > $max_vaule_price) {
    $price_plan_from = $max_vaule_price;
}
if ($price_plan_to > $max_vaule_price) {
    $price_plan_to = $max_vaule_price;
}
if ($win_price_from > $max_vaule_price) {
    $win_price_from = $max_vaule_price;
}
if ($win_price_to > $max_vaule_price) {
    $win_price_to = $max_vaule_price;
}

$is_elas = ($module_config[$module_name]['elas_use'] and $type_search == 1 and empty($static) and empty($vip_static)) ? true : false;
list($sfrom1, $sto1) = setMySqlRangeTimes($is_elas, $sfrom, $sto, $q, $error);
if (NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and ($type_user == 2 or $type_user == 3)) {
    // view chỉ xem dc tin cũ
    $sto1 = $close_time_dauthau;
    if ($sfrom1 > $sto1) {
        $sfrom1 = $sto1 - (86400 * 30);
    }
}

$_GET['sfrom'] = nv_date('d/m/Y', $sfrom1);
$_GET['sto'] = nv_date('d/m/Y', $sto1);

$home_page = $nv_Request->get_int('home_page', 'get', 0);
$per_page = ($home_page == 1) ? 10 : 20;
$page = $nv_Request->get_page('page', 'post,get', 1);
if ($page < 1) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_wrong_page') . $btn, 'danger');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

/**
 * thao: cho sửa dùm anh nếu page > 100 báo lỗi:
 * Vui lòng thay đổi tiêu chí tìm kiếm để có thông tin bạn cần
 */
if ($page > 100) {
    $nv_BotManager->setPrivate();
    $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
    $contents = nv_theme_alert($nv_Lang->getModule('notice'), $nv_Lang->getModule('note_max_searchpage') . $btn);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}
$arr_tbmt = [];

$arr_solicitor_id = [];
$list_solicitor = [];

$arr_bidder = [];
$rids = [];
$array_data_result = [];

// kiểm tra sử dụng elastic search hay mysql
if ($is_elas) {
    require NV_ROOTDIR . '/modules/' . $module_name . '/search/listresult_elastic.php';
} else {
    require NV_ROOTDIR . '/modules/' . $module_name . '/search/listresult_mysql.php';
}

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $current_op;
$filter_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=filters';
$global_lang_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . $other_lang . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $current_other_op;
if (!empty($arr_unexist_key)) {
    $filter_url .= "&add=1";
    $filter_key = implode(',', $arr_unexist_key);
    $filter_url .= "&key=" . $filter_key;
}

if (!empty($code)) {
    $base_url .= "&code=" . $code;
    $global_lang_url .= "&code=" . $code;
}

if ($nv_Request->isset_request('type_search', 'get')) {
    $base_url .= '&type_search=' . $type_search;
    $global_lang_url .= '&type_search=' . $type_search;
}
if ($nv_Request->isset_request('type_info', 'get')) {
    $base_url .= '&type_info=' . $type_info;
    $global_lang_url .= '&type_info=' . $type_info;
}
if ($nv_Request->isset_request('type_info2', 'get')) {
    $base_url .= '&type_info2=' . $type_info2;
    $global_lang_url .= '&type_info2=' . $type_info2;
}
if ($nv_Request->isset_request('search_type_content', 'get')) {
    $base_url .= '&search_type_content=' . $search_type_content;
    $global_lang_url .= '&search_type_content=' . $search_type_content;
}

if ($nv_Request->isset_request('par_search', 'get')) {
    $base_url .= '&par_search=' . $par_search;
    $global_lang_url .= '&par_search=' . $par_search;
}

if ($nv_Request->isset_request('searchkind', 'get')) {
    $base_url .= '&searchkind=' . $search_kind;
    $global_lang_url .= '&searchkind=' . $search_kind;
}

if ($goods > 0) {
    $base_url .= '&goods=' . $goods;
    $global_lang_url .= '&goods=' . $goods;
}

if ($type_kqlcnt > 0) {
    $base_url .= '&type_kqlcnt=' . $type_kqlcnt;
    $global_lang_url .= '&type_kqlcnt=' . $type_kqlcnt;
}

if (!empty($bidfieid)) {
    foreach ($bidfieid as $f) {
        $base_url .= "&bidfieid[]=" . $f;
        $global_lang_url .= "&bidfieid[]=" . $f;
        $filter_url .= "&bidfieid[]=" . $f;
    }
}

$filter_url .= '&type_search=' . $type_search;
$filter_url .= '&type_info=' . $type_info;
$filter_url .= '&type_info2=' . $type_info2;
$filter_url .= '&search_type_content=' . $search_type_content;
$filter_url .= '&par_search=' . $par_search;
$filter_url .= '&searchkind=' . $search_kind;
$filter_url .= '&goods=' . $goods;
if (!empty($q)) {
    $base_url .= '&q=' . urlencode($q);
    $global_lang_url .= '&q=' . urlencode($q);
    $base_result_url .= '&amp;q=' . urlencode($q);
}
if (!empty($sfrom)) {
    $base_url .= "&sfrom=" . $sfrom;
    $global_lang_url .= "&sfrom=" . $sfrom;
    $base_result_url .= "&amp;sfrom=" . $sfrom;
}

if (!empty($sto)) {
    $base_url .= "&sto=" . $sto;
    $global_lang_url .= "&sto=" . $sto;
    $base_result_url .= "&amp;sto=" . $sto;
}
if (!empty($_idprovince)) {
    foreach ($_idprovince as $value) {
        $base_url .= '&idprovincekq[]=' . $value;
        $global_lang_url .= '&idprovincekq[]=' . $value;
    }
}

if ($solicitor_id > 0) {
    $base_url .= '&solicitor_id=' . $solicitor_id;
    $global_lang_url .= '&solicitor_id=' . $solicitor_id;
}
if ($static >= 1 && $static < 5) {
    $base_url .= '&static=' . $static;
    $global_lang_url .= '&static=' . $static;
}
if (!empty($vip_static)) {
    $base_url .= '&vip_static=' . $vip_static;
    $global_lang_url .= '&vip_static=' . $vip_static;
}
if ($win_price_from > 0 && $win_price_from <= $max_vaule_price) {
    $base_url .= '&win_price_from=' . $win_price_from;
    $global_lang_url .= '&win_price_from=' . $win_price_from;
    $filter_url .= '&win_price_from=' . $win_price_from;
}
if ($win_price_to > 0 && $win_price_to <= $max_vaule_price) {
    $base_url .= '&win_price_to=' . $win_price_to;
    $global_lang_url .= '&win_price_to=' . $win_price_to;
    $filter_url .= '&win_price_to=' . $win_price_to;
}
if ($price_plan_to > 0 && $price_plan_to <= $max_vaule_price) {
    $base_url .= '&price_plan_to=' . $price_plan_to;
    $global_lang_url .= '&price_plan_to=' . $price_plan_to;
    $filter_url .= '&price_plan_to=' . $price_plan_to;
}
if ($price_plan_from > 0 && $price_plan_from <= $max_vaule_price) {
    $base_url .= '&price_plan_from=' . $price_plan_from;
    $global_lang_url .= '&price_plan_from=' . $price_plan_from;
    $filter_url .= '&price_plan_from=' . $price_plan_from;
}

if ($catressult > 0) {
    $base_url .= "&catressult=" . $catressult;
    $global_lang_url .= "&catressult=" . $catressult;
    $filter_url .= "&catressult=" . $catressult;
}
if (!empty($key_search2)) {
    $base_url .= '&q2=' . urlencode($key_search2);
    $global_lang_url .= '&q2=' . urlencode($key_search2);
    $filter_url .= '&key2=' . urlencode($key_search2);
    $base_result_url .= '&amp;q2=' . urlencode($key_search2);
}
if (!empty($search_one_key)) {
    $base_url .= '&search_one_key=' . $search_one_key;
    $global_lang_url .= '&search_one_key=' . $search_one_key;
    $filter_url .= '&search_one_key=' . $search_one_key;
}
if (!empty($without_key)) {
    // Tìm theo từ khóa loại trừ
    $base_url .= '&without_key=' . urlencode($without_key);
    $global_lang_url .= '&without_key=' . urlencode($without_key);
    $filter_url .= '&without_key=' . urlencode($without_key);
    $base_result_url .= '&amp;without_key=' . urlencode($without_key);
}
if ($type_bid > 0) {
    $base_url .= '&type_bid=' . $type_bid;
    $global_lang_url .= '&type_bid=' . $type_bid;
}
if ($type_choose_id > 0) {
    $base_url .= '&type_choose_id=' . $type_choose_id;
    $global_lang_url .= '&type_choose_id=' . $type_choose_id;
}
if ($is_advance) {
    $base_url .= "&is_advance=1";
    $global_lang_url .= "&is_advance=1";
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);

$array_data = handle_array_result($array_data, $type_search, $show_vip);

// Xử lý tìm kiếm ajax từ module seek
if ($nv_Request->isset_request('ajaxsearch', 'post')) {
    $respon = [];
    if ($nv_Request->get_title('quicksearchform', 'get', '') === NV_CHECK_SESSION) {
        $index = 0;
        $respon['num_items'] = number_format($num_items, 0, '', '.');
        foreach ($array_data as $key => $value) {
            $respon['data'][$index]['link'] = nv_url_rewrite($array_data[$key]['link'], true);
            $value = array_map(function ($a) use ($l, $q) {
                if (is_string($a)) {
                    return BoldKeywordInStr($a, $q, (isset($l) && $l === 0) ? 'OR' : 'AND');
                } else {
                    return $a;
                }
            }, $value);
            $respon['data'][$index]['title'] = $value['title'];
            $respon['data'][$index]['content'] = '<b>' . $nv_Lang->getModule('ben_moi_thau') . '</b>' . ': ' . $value['solicitor_title'] . ' - ' . '<b>' . $nv_Lang->getModule('finish_time_sort') . '</b>' . ': ' . $value['finish_time'];
            $index += 1;
            if ($index == 3) {
                break;
            }
        }
        if ($type_search == 2) {
            $respon['view_full_page'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNDT() . '&q=' . urlencode($q), true);
        } else {
            $respon['view_full_page'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT() . '&q=' . urlencode($q), true) . ((isset($l) && $l === 0) ? '&searchkind=1&is_advance=1' : '');
        }
    }
    nv_jsonOutput($respon);
}
// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
// #2415: Thêm hằng chặn index: KQLCNT
$query_params = [];
parse_str(parse_url($page_url, PHP_URL_QUERY), $query_params);
// Danh sách các tham số cần xóa
$params_to_remove = [
    'amp;' . NV_NAME_VARIABLE,
    'amp;' . NV_OP_VARIABLE,
    NV_LANG_VARIABLE
];
if (!empty($_idprovince)) {
    $params_to_remove[] = 'idprovincekq';
}
$query_params = array_diff_key($query_params, array_flip($params_to_remove));
$has_other_query_params = false;
foreach ($query_params as $key => $value) {
    if ($key !== 'amp;page') {
        $has_other_query_params = true;
        break;
    }
}
if (!empty($q) || empty($array_data) || $has_other_query_params) {
    $nv_BotManager->setFollow()->setNoIndex();
}
$canonicalUrl = getCanonicalUrl($page_url);
$urlappend = '&amp;page=';
// Kiểm tra đánh số trang
betweenURLs($page, ceil($num_items / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

$prof_ids = [];
$profile_ids = [];
$bid_codes = [];
foreach ($array_data_result as $key => $value) {
    if (!empty($value['prof_id'])) {
        $prof_ids[$value['prof_id']] = $value['prof_id'];
    }
    if (!empty($value['profile_id'])) {
        $profile_ids[$value['profile_id']] = $value['profile_id'];
    }
    if (!empty($value['bid_code'])) {
        $bid_codes[$value['bid_code']] = $value['bid_code'];
    }
}

$arr_profile = fetchProfileData($profile_ids);

$arr_prof = fetchProfileData($prof_ids);
foreach ($arr_prof as $id => &$prof) {
    $prof['link_winning'] = URL_DTNET_SITE . NV_LANG_DATA . '/' . $dtnet_module_profile . '/' . $prof['prof_alias'] . '/';
}

$array_tbmt = [];
if (!empty($bid_codes)) {
    $search_bid_codes = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_result_host'], $module_config[$module_name]['elas_result_port'], 'bids_rows', $module_config[$module_name]['elas_result_user'], $module_config[$module_name]['elas_result_pass']);
    foreach ($bid_codes as $bid_code) {
        $search_tbmt = ['must' => [['match' => ['code' => ['query' => $bid_code]]]]];
        $array_query_elastic_dtnet_tbmt = ['query' => ['bool' => $search_tbmt]];

        $response = $search_bid_codes->search_data($db_config['prefix'] . '_bids_rows', $array_query_elastic_dtnet_tbmt);
        foreach ($response['hits']['hits'] as $v) {
            if (!empty($v['_source'])) {
                $view = $v['_source'];
                $array_tbmt[$view['code']] = $view;
            }
        }
    }
}

// Gộp kết quả từ các truy vấn vào `$array_data_result`
foreach ($array_data_result as $key => $value) {
    if (
        empty($value['profile_id']) || !isset($arr_profile[$value['profile_id']]) ||
        empty($value['prof_id']) || !isset($arr_prof[$value['prof_id']]) ||
        empty($value['bid_code']) || !isset($array_tbmt[$value['bid_code']])
    ) {
        unset($array_data_result[$key]);
        continue;
    }

    // Gộp từ `profile_id`
    $profile = $arr_profile[$value['profile_id']];
    $array_data_result[$key]['prof_code_profile'] = $profile['prof_code'];
    $array_data_result[$key]['prof_name_profile'] = $profile['prof_name'];
    $array_data_result[$key]['prof_alias_profile'] = $profile['prof_alias'];
    $array_data_result[$key]['link_bid_invitation'] = $profile['link_invector'];

    // Gộp từ `prof_id`
    $prof = $arr_prof[$value['prof_id']];
    $array_data_result[$key]['prof_code_prof'] = $prof['prof_code'];
    $array_data_result[$key]['prof_name_prof'] = $prof['prof_name'];
    $array_data_result[$key]['prof_alias_prof'] = $prof['prof_alias'];
    $array_data_result[$key]['link_winning'] = $prof['link_winning'];

    // Gộp từ `bid_code`
    $tbmt = $array_tbmt[$value['bid_code']];
    $array_data_result[$key]['alias_inform'] = $tbmt['alias'];
    $array_data_result[$key]['code_inform'] = $tbmt['code'];
    $array_data_result[$key]['title_inform'] = $tbmt['title'];
    $array_data_result[$key]['link_inform'] = URL_DTNET_SITE . NV_LANG_DATA . '/' . $dtnet_module_bids . '/' . $dtnet_module_bids_tbmt . '/' . $tbmt['alias'] . '-' . $tbmt['code'] . $global_config['rewrite_exturl'];
    $array_data_result[$key]['link_result'] = URL_DTNET_SITE . NV_LANG_DATA . '/' . $dtnet_module_bids . '/' . $dtnet_module_bids_result . '/' . $tbmt['alias'] . '-' . $tbmt['code'] . $global_config['rewrite_exturl'];

    // Thêm `link_see_more`
    $array_data_result[$key]['link_see_more'] = $base_result_url;
}

$contents = nv_theme_bidding_list_result($array_data, $generate_page, $error, $error_money, $filter_url, $arr_unexist_key, $type_search, $array_data_result);
$contents .= '<br/>' . $nv_Lang->getModule('search_in') . ': ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . ' - ' . $nv_Lang->getModule('number_result') . ': ' . number_format($num_items, 0, ",", ".");
include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

// Xử lý dữ liệu để hiển thị
function handle_array_result($array_data, $type_search, $show_vip)
{
    global $db, $module_data, $module_name, $module_info, $global_config, $sfrom1, $sto1, $arr_tbmt, $arr_solicitor_id, $list_solicitor, $arr_bidder, $rids;

    if (!empty($array_data)) {
        // Tim link TBMT
        $arr_tbmt = array_unique($arr_tbmt);
        $arr_tbmt = array_filter($arr_tbmt, function ($el) use ($db) {
            return $db->quote($el);
        });
        $arr_tbmt_sql = "'" . implode("','", $arr_tbmt) . "'";

        // Lấy alias nhà mời thầu
        $arr_solicitor_id = array_unique($arr_solicitor_id);
        $solicitor_list = implode(',', $arr_solicitor_id);
        $db->sqlreset();
        $db->select('id,alias,title,solicitor_code, org_code, english_name')->from(BID_PREFIX_GLOBAL . '_solicitor');
        if (!empty($arr_solicitor_id)) {
            $db->where('id IN(' . $solicitor_list . ')');
        }

        $sth = $db->prepare($db->sql());
        $sth->execute();
        while ($info_solicitor = $sth->fetch()) {
            $list_solicitor[$info_solicitor['id']] = [
                $info_solicitor['alias'],
                $info_solicitor['title'],
                (!empty($info_solicitor['org_code']) ? $info_solicitor['org_code'] : $info_solicitor['solicitor_code']),
                $info_solicitor['english_name']
            ];
        }
        $sth->closeCursor();

        $min_col = array_column($array_data, 'time_open_static');
        $min_col = (!empty($min_col) ? min($min_col) : 0);

        $max_col = array_column($array_data, 'time_open_static');
        $max_col = (!empty($max_col) ? max($max_col) : 0);

        // Lấy thông tin khách VIP trong khoảng thời gian tìm kiếm
        if ($show_vip) {
            $min_time = strtotime(date('Y/n/1', (!empty($min_col) ? $min_col : $sfrom1)));
            $max_time = strtotime('+1 month', strtotime(date('Y/n/1', (!empty($max_col) ? $max_col : $sto1))));
            $min_time = strtotime(date('Y/n/1', (!empty(array_column($array_data, 'time_open_static')) ? min(array_column($array_data, 'time_open_static')) : $sfrom1)));
            $max_time = strtotime('+1 month', strtotime(date('Y/n/1', (!empty(array_column($array_data, 'time_open_static')) ? max(array_column($array_data, 'time_open_static')) : $sto1))));
            $sql = 'SELECT tax, GROUP_CONCAT(DISTINCT vip) as vip_list FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE
                (from_time <= ' . $min_time . ' AND end_time >= ' . $min_time . ') OR
                (from_time >= ' . $min_time . ' AND from_time < ' . $max_time . ') GROUP BY tax';
            $result = $db->query($sql);
            while ($row = $result->fetch()) {
                $row['vip_list'] = array_filter(explode(',', $row['vip_list']));
                !empty($row['tax']) && $array_vip[$row['tax']] = $row['vip_list'];
            }
        }
        // Lấy thong tin của nha thầu trúng thầu
        if (!empty($rids)) {
            $rids = array_unique($rids);
            $rids = implode(",", $rids);
            $result_business = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_result_business WHERE resultid IN (" . $rids . ") ORDER BY resultid DESC");
            while ($_row = $result_business->fetch()) {
                !isset($array_data[$_row['resultid']]['bidder_list']) && $array_data[$_row['resultid']]['bidder_list'] = [];
                $array_data[$_row['resultid']]['bidder_list'][] = [
                    'name' => $_row['bidder_name'],
                    'bid' => 0,
                    'no_business_licence' => $_row['no_business_licence'],
                    'vip_list' => !empty($array_vip[$_row['no_business_licence']]) ? $array_vip[$_row['no_business_licence']] : []
                ];
                !empty($_row['no_business_licence']) && $arr_bidder[] = $_row['no_business_licence'];
            }
        }

        if (!empty($arr_bidder)) {
            $arr_bidder = array_unique($arr_bidder);
            $arr_bidder_sql = "'" . implode("','", $arr_bidder) . "'";
            $arr_bidder = [];
            $info_res = $db->query("SELECT id, code, companyname FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE code IN (" . $arr_bidder_sql . ")");
            while ($_row = $info_res->fetch()) {
                $arr_bidder[$_row['code']] = [
                    $_row['id'],
                    $_row['companyname']
                ];
            }
        }

        // Xử lý lại nhà mời nhầu
        foreach ($array_data as $key => $data) {
            $alias_re = isset($data['alias']) ? $data['alias'] : strtolower(change_alias($array_data[$key]['title']));
            if (!empty($data['bid_id'])) {
                $array_data[$key]['link_tbmt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . ($type_search == 2 ? '/' . Url::getTBMDT() . '/' : '/' . Url::getTBMT() . '/') . $alias_re . '-' . $data['bid_id'] . $global_config['rewrite_exturl'];
                $array_data[$key]['tbmt_id'] = $data['bid_id'];
            } else {
                $tbmt = $db->query('SELECT id, so_tbmt, goi_thau FROM ' . NV_PREFIXLANG . '_' . $module_data . '_row WHERE so_tbmt IN (' . $arr_tbmt_sql . ')');
                $arr_tbmt = [];
                $arr_title_tbmt = [];
                while ($info_tbmt = $tbmt->fetch()) {
                    $arr_tbmt[$info_tbmt['so_tbmt']] = $info_tbmt['id'];
                    $arr_title_tbmt[$info_tbmt['so_tbmt']] = $info_tbmt['goi_thau'];
                }

                if (isset($arr_tbmt[$data['code']]) and $arr_title_tbmt[$data['code']] == $data['title']) {
                    $array_data[$key]['link_tbmt'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['view'] . ($type_search == 2 ? '/' . Url::getTBMDT() . '/' : '/' . Url::getTBMT() . '/') . $alias_re . '-' . $arr_tbmt[$data['code']] . $global_config['rewrite_exturl'];
                    $array_data[$key]['tbmt_id'] = $arr_tbmt[$data['code']];
                } else {
                    $array_data[$key]['link_tbmt'] = '';
                }
            }

            if (isset($list_solicitor[$data['solicitor_id']])) {
                $data['alias_solicitor'] = $list_solicitor[$data['solicitor_id']][0];
                $array_data[$key]['solicitor_title'] = $list_solicitor[$data['solicitor_id']][1];
                // alias đa ngôn ngữ
                $list_solicitor[$data['solicitor_id']][3] = !empty($list_solicitor[$data['solicitor_id']][3]) ? trim($list_solicitor[$data['solicitor_id']][3]) : '';
                if (NV_LANG_DATA != 'vi' && !empty($list_solicitor[$data['solicitor_id']][3])) {
                    $data['alias_solicitor'] = change_alias($list_solicitor[$data['solicitor_id']][3]);
                    $array_data[$key]['solicitor_title'] = $list_solicitor[$data['solicitor_id']][3];
                }

                $array_data[$key]['link_solicitor'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['solicitor'] . '/' . $data['alias_solicitor'] . '-' . $data['solicitor_id'];
                $array_data[$key]['solicitor_code'] = $list_solicitor[$data['solicitor_id']][2];
            } else {
                $data['alias_solicitor'] = '';
                $array_data[$key]['link_solicitor'] = '';
                $array_data[$key]['solicitor_title'] = isset($array_data[$key]['investor']) ? $array_data[$key]['investor'] : $array_data[$key]['soclictor'];
                $array_data[$key]['solicitor_code'] = '';
            }

            $array_data[$key]['bidder_list'][0]['link'] = '';
            if ($type_search == 2) {
                $no_business_licence = $array_data[$key]['bidder_list'][0]['no_business_licence'];
                if (isset($arr_bidder[$no_business_licence])) {
                    $array_data[$key]['bidder_list'][0]['bid'] = $arr_bidder[$no_business_licence][0];
                    empty($array_data[$key]['bidder_list'][0]['name']) && $array_data[$key]['bidder_list'][0]['name'] = $arr_bidder[$no_business_licence][1];
                    $array_data[$key]['bidder_list'][0]['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=businesslistings&' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '/' . change_alias($arr_bidder[$no_business_licence][1]) . '-' . $arr_bidder[$no_business_licence][0], true);
                }
            } else {
                $size = sizeof($array_data[$key]['bidder_list']);
                if ($size) {
                    for ($i = 0; $i < $size; $i++) {
                        $no_business_licence = '';
                        if (isset($array_data[$key]['bidder_list'][$i]['no_business_licence'])) {
                            $no_business_licence = $array_data[$key]['bidder_list'][$i]['no_business_licence'];
                        }

                        if (isset($arr_bidder[$no_business_licence])) {
                            $array_data[$key]['bidder_list'][$i]['bid'] = $arr_bidder[$no_business_licence][0];
                            empty($array_data[$key]['bidder_list'][$i]['name']) && $array_data[$key]['bidder_list'][$i]['name'] = $arr_bidder[$no_business_licence][1];
                            $array_data[$key]['bidder_list'][$i]['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=businesslistings&' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '/' . change_alias($arr_bidder[$no_business_licence][1]) . '-' . $arr_bidder[$no_business_licence][0], true);
                        }
                    }
                }
            }
        }
    }

    return $array_data;
}

// Lấy dữ liệu mới nhất
function handle_array_data($type_search)
{
    global $db, $module_data, $client_info, $global_config, $module_name, $op, $module_info, $arr_tbmt, $arr_solicitor_id, $arr_bidder, $rids;

    if ($type_search == 1) {
        $_limit_time = NV_CURRENTTIME - 7 * 86400;
        $db->sqlreset()
            ->select('*')
            ->from(NV_PREFIXLANG . '_' . $module_data . '_result')
            ->where('finish_time > ' . $_limit_time)
            ->order('finish_time DESC')
            ->limit(20);
        $sth = $db->prepare($db->sql());
        $sth->execute();
    } else {
        $db->sqlreset()
            ->select('*')
            ->from(NV_PREFIXLANG . '_' . $module_data . '_result_project')
            ->order('post_time DESC')
            ->limit(20);
        $sth = $db->prepare($db->sql());
        $sth->execute();
    }

    $min_time = $max_time = 0;
    while ($view = $sth->fetch()) {
        $view['link_edit'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $view['id'];
        $view['link_delete'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $view['id'] . '&amp;delete_checkss=' . md5($view['id'] . NV_CACHE_PREFIX . $client_info['session_id']);

        $view['bidder_list'] = [];
        $alias_re = isset($view['alias']) ? $view['alias'] : strtolower(change_alias($view['title']));

        if ($type_search == 2) {
            $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNDT() . '/' . $alias_re . '-' . $view['id'] . $global_config['rewrite_exturl'];
            $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listresult&type=1&solicitor_id=' . $view['solicitor_id'];
            if (isset($view['finish_time'])) {
                $view['finish_time'] = date("d/m/Y H:i", $view['finish_time']);
                $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
            } else {
                $view['finish_time'] = date("d/m/Y H:i", $view['post_time']);
                $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
            }

            $view['bidder_list'][] = [
                'name' => $view['bidder_name'],
                'bid' => 0,
                'no_business_licence' => $view['no_business_licence']
            ];
            !empty($view['no_business_licence']) && $arr_bidder[] = $view['no_business_licence'];
        } else {
            $view['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['result'] . '/' . Url::getKQLCNT() . '/' . $alias_re . '-' . $view['id'] . $global_config['rewrite_exturl'];
            $view['link_search'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=listresult&type=0&solicitor_id=' . $view['solicitor_id'];

            if (isset($view['finish_time'])) {
                if (empty($min_time)) {
                    $min_time = $max_time = $view['finish_time'];
                }
                $view['finish_time'] < $min_time && $min_time = $view['finish_time'];
                $view['finish_time'] > $max_time && $max_time = $view['finish_time'];
                $view['finish_time'] = date("d/m/Y H:i", $view['finish_time']);
                $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
            } else {
                $view['finish_time'] = date("d/m/Y H:i", $view['post_time']);
                $view['finish_time'] = explode('00:00', $view['finish_time'])[0];
            }
            $rids[] = $view['id'];
        }

        $array_data[$view['id']] = $view;
        $arr_solicitor_id[] = $view['solicitor_id'];
        $arr_tbmt[] = $view['code'];
    }
    $sth->closeCursor();

    return $array_data;
}
