<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
if (!defined('NV_SYSTEM')) {
    die('Stop!!!');
}

define('NV_IS_MOD_BIDDING', true);
use NukeViet\Dauthau\Share;
use NukeViet\Dauthau\Url;

$TBMT = 'NukeViet\Dauthau\TBMT';
define('BIDDING_ROW_TABLE', $TBMT::$sql_table['bidding_row']);
define('BIDDING_DETAIL_TABLE', $TBMT::$sql_table['bidding_detail']);
define('BIDDING_PROJECT_ROW_TABLE', $TBMT::$sql_table['bidding_project_row']);
define('BIDDING_PROJECT_DETAIL_TABLE', $TBMT::$sql_table['bidding_project_detail']);
define('BIDDING_UPDATE_USER_TABLE', $TBMT::$sql_table['bidding_update_user']);
define('BIDDING_CUSTOMS_TABLE', $TBMT::$sql_table['bidding_customs']);
define('BIDDING_CUSTOMS_ROWFILES_TABLE', $TBMT::$sql_table['bidding_customs_rowfiles']);
define('BIDDING_PLANS_TABLE', $TBMT::$sql_table['bidding_plans']);
define('DUTHAU_URL_CRAWLS_TABLE', $cr_config['prefix'] . "_duthau_url");
define('DUTHAU_PLAN_URL_CRAWLS_TABLE', $cr_config['prefix'] . "_vi_bidding_plans_url");
define('TBL_BIDDING_RESULT_BUSINESS', NV_PREFIXLANG . '_bidding_result_business');
define('TBL_BIDDING_SOLICTOR', BID_PREFIX_GLOBAL . '_solicitor');
define('TBL_BIDDING_ROW', NV_PREFIXLANG . '_bidding_row');
define('TBL_BIDDING_OPEN_DETAIL', NV_PREFIXLANG . '_bidding_open_detail');
define('TBL_BIDDING_PLANS', NV_PREFIXLANG . '_bidding_plans');
define('TBL_BIDDING_PLANS_CONTRACT', NV_PREFIXLANG . '_bidding_plans_contract');
define('TBL_BIDDING_PREQUALIFICATION', NV_PREFIXLANG . '_bidding_prequalification');
define('TBL_BIDDING_DETAIL', NV_PREFIXLANG . '_bidding_detail');
define('TBL_BIDDING_RESULT', NV_PREFIXLANG . '_bidding_result');
define('TBL_PROJECT_INVESTMENT', NV_PREFIXLANG . '_project_investment');
define('TBL_BIDDING_SOLICTOR_STATIC', BID_PREFIX_GLOBAL . '_solicitor_static');
define('TBL_BIDDING_CUSTOM', BID_PREFIX_GLOBAL . '_customs');
define('TBL_BUSINESS_INFO', BUSINESS_PREFIX_GLOBAL . '_info');

$nv_Lang->setModule('icon_vneps', file_get_contents(NV_ROOTDIR . '/themes/dauthau/images/bidding/icon_vneps.svg'));

// array những gói vip có tính năng nâng cao
$vip_has_advance_feature = [
    89, // x2
    1, // vip1
    2, // vip2
];

// cookie 7 ngày
$expire_cookie = $module_config[$module_name]['expire_cookie'] * 86400;

// set url cho request-quote (Yêu cầu báo giá)
if ($op == $module_info['alias']['request-quote']) {
    if (!empty($array_op) and preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/', $array_op[1], $m)) {
        $op = $module_info['alias']['request-quote-detail'];
    }
}

// set url cho bidding-contract (Hợp đồng gói thầu)
if ($op == $module_info['alias']['bidding-contract']) {
    $id = 0;
    if (!empty($array_op) and preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/', $array_op[1], $m)) {
        $op = $module_info['alias']['bidding-contract-detail'];
        if (isset($array_op[1]) and preg_match('/^([a-zA-Z0-9\-]+)\-([0-9]+)$/i', $array_op[1], $m)) {
            $id = intval($m[2]);
            $alias = $m[1];
        }
    }
}

global $array_linhvuc;
$array_linhvuc = [
    1 => $nv_Lang->getModule('field_1'),
    3 => $nv_Lang->getModule('field_2'),
    5 => $nv_Lang->getModule('field_3'),
    10 => $nv_Lang->getModule('field_4'),
    15 => $nv_Lang->getModule('field_5'),
    20 => $nv_Lang->getModule('field_20')
];

$other_lang = NV_LANG_DATA == 'en' ? 'vi' : 'en';
$other_lang_prefix = $db_config['prefix'] . '_' . $other_lang;
$other_lang_module_info = nv_site_mods($other_lang)[$module_name];
global $arr_unset;
$arr_unset = [
    "type_bid_id",
    "name_search",
    "last_crawl",
    "content_full",
    "elasticsearch",
    "exported_sitemap",
    "log_change",
    "name_staff",
    "content",
    "url",
    "upcount",
    "totalview",
    "update_leads",
    "array_profile",
    "log_change",
    "data_process",
    "alias",
    "id",
    "totalview",
    "update_data",
    "type_url",
    "plan_id_msc",
    "catid",
    "ho_so_point",
    "msc_typeid",
    "is_vip5",
    "sourceurl",
    "time_hide_end",
    "hide_vip3",
    "update_leads",
    "array_profile",
    "userid",
    "gmaps",
    "url_run",
    "time_url",
    "date_crawl",
    "last_crawl",
    "gettime",
    "get_time",
    "fgettime",
    "url",
    "quyet_dinh_point",
    "vip_del_time",
    "updatebussiness",
    "ho_so_del_time",
    "point",
    "phone_search",
    "translator",
    "updatetime",
    "cancel_file_id",
    "investor_id",
    'solicitor_id',
    'html_phamvicungcap',
    'update_static',
    'trangthai_msc_new'
];
// Lấy phân mục

$_array_phanmuc_bids = [];
$_sql = 'SELECT id, parent_id, title_' . NV_LANG_DATA . ' as title , alias_' . NV_LANG_DATA . ' as alias FROM ' . BID_PREFIX_GLOBAL . '_phanmuc WHERE status=1 ORDER BY id ASC';
$_array_listphanmucid = $nv_Cache->db($_sql, 'id', NV_LANG_DATA . '_' . $module_name);
foreach ($_array_listphanmucid as $_row) {
    $_array_phanmuc_bids[$_row['parent_id']][$_row['id']] = $_row;
}
if (!empty($_array_phanmuc_bids)) {
    $_array_phanmuc_bids[0][-1] = array(
        'id' => -1,
        'parent_id' => 0,
        'title' => $nv_Lang->getModule('no_title'),
        'alias' => $nv_Lang->getModule('alias_chua_phan_loai')
    );
    $_array_phanmuc_bids[-1][0] = array(
        'parent_id' => 0,
        'title' => $nv_Lang->getModule('no_title'),
        'alias' => $nv_Lang->getModule('alias_chua_phan_loai')
    );
}

$cache = $nv_Cache->getItem($module_name, NV_LANG_DATA. '_list_vsic.cache', '3600');
if ($cache !== 'false' && $cache != '') {
    //Nếu đã lưu cache thì đọc luôn
    $list_code_vsic = json_decode($cache, true);
} else {
    $_sql = 'SELECT id, level, code, title, alias FROM ' . NV_PREFIXLANG . '_industry WHERE status = 1 ORDER BY id ASC';
    $res_vsic = $db->query($_sql);
    while ($row_vsic = $res_vsic->fetch()) {
        $row_vsic['link'] = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . ($module_info['alias']['vsic-industry'] ?? '') . '/' . $row_vsic['alias'] . '-' . $row_vsic['code'];
        $list_code_vsic[$row_vsic['code']] = $row_vsic;
    }
    $res_vsic->closeCursor();
    $cache_json = json_encode($list_code_vsic);
    $nv_Cache->setItem($module_name, NV_LANG_DATA . '_list_vsic.cache', $cache_json, 600);
}
$list_code_vsic[0] = [
    'id' => 0,
    'code' => 0,
    'level' => 1,
    'title' => mb_convert_case($nv_Lang->getModule('no_title'), MB_CASE_UPPER),
    'alias' => $nv_Lang->getModule('alias_chua_phan_loai'),
];

// kiểm tra loại tài khoản tại đây luôn
// 0, 1: thành viên, khách ẩn toàn bộ thông tin
// 2: view: xem dc các tin cũ
// 3, vip :k ẩn
// 4: vip dc phân quyền

global $type_user;
if (defined('NV_IS_USER')) {
    $type_user = 1;

    if (!empty($global_array_vip)) {
        foreach ($global_array_vip as $key => $value) {
            if ($key != 99 && $key != 77) {
                $type_user = 3;
                break;
            } else {
                $type_user = 2;
            }
        }
    }

    // kiểm tra có các tk dc phân quyền hay không
    if (!empty($arr_customs_permission_view_detail)) {
        $type_user = 4;
    }
} else {
    $type_user = 0;
}
/**
 *
 * @since 17/05/2021
 * <AUTHOR>
 *         Sửa lỗi các op cũ không còn vào được khi đánh alias cho các op đó theo yêu cầu ở issue này
 *         https://vinades.org/dauthau/dauthau.info/-/issues/339
 */
if ($op == 'main') {
    $array_changealias_op = [
        'plans' => 'kehoach',
        'prequalification' => 'moisotuyen',
        'resultpq' => 'ketquasotuyen',
        'view' => 'thongbao',
        'viewopen' => 'ketquamothau',
        'result' => 'ketqua',
        'projectdetail' => 'duanmoicongbo',
        'viewopenpq' => 'ketquamosotuyen',
        'planoverall' => 'kehoachtongthe'
    ];

    if (isset($array_op[0]) and isset($array_changealias_op[$array_op[0]]) and isset($module_info['funcs'][$array_changealias_op[$array_op[0]]])) {
        $op = $array_changealias_op[$array_op[0]];
    }
}

//Set url riêng cho TBMT & TBMDT
$online_bid = change_alias(strtolower($nv_Lang->getModule('online_bid')));
$offline_bid = change_alias(strtolower($nv_Lang->getModule('offline_bid')));
$small_org_bid = change_alias(strtolower($nv_Lang->getModule('small_org_bid')));
if ($op == $module_info['alias']['view'] && empty($array_op) && !$nv_Request->isset_request('nocache', 'post,get')
    || isset($array_op[1]) && $array_op[1] == Url::getTBMT() && (empty($array_op[2]) || in_array($array_op[2], [$online_bid, $offline_bid, $small_org_bid]))
    || isset($array_op[1]) && $array_op[1] == Url::getTBMDT() && empty($array_op[2])
    || isset($array_op[1]) && $array_op[1] == Url::getTBMT() && isset($array_op[2]) && $array_op[2] == $module_info['alias']['tinh-thanh'] && isset($array_op[3])
    || $op == $module_info['alias']['view'] && isset($array_op[1]) && $array_op[1] == Url::getTBMT() && (empty($array_op[2]) || in_array($array_op[2], [$online_bid, $offline_bid, $small_org_bid]))
    || $op == $module_info['alias']['view'] && isset($array_op[1]) && $array_op[1] == Url::getTBMDT() && empty($array_op[2])

) {
    $notice_page = true;
    $op = $module_info['alias']['detail'];

//Set url riêng cho Kết quả sơ tuyển
} elseif ($op == $module_info['alias']['resultpq'] && empty($array_op) && !$nv_Request->isset_request('nocache', 'post,get')
    || $op == $module_info['alias']['resultpq'] && isset($array_op[1]) && $array_op[1] == Url::getNT() && empty($array_op[2])
    || $op == $module_info['alias']['resultpq'] && isset($array_op[1]) && $array_op[1] == Url::getNDT() && empty($array_op[2])
) {
    $listresultpq_page = true;
    $op = $module_info['alias']['listresultpq'];

//Set url riêng cho Kết quả lựa chọn nhà thầu/nhà đầu tư
} elseif ($op == $module_info['alias']['result'] && empty($array_op) && !$nv_Request->isset_request('nocache', 'post,get')
    || $op == $module_info['alias']['result'] && isset($array_op[1]) && $array_op[1] == Url::getKQLCNT() && (empty($array_op[2]) || in_array($array_op[2], ['linked', 'unlinked']))
    || $op == $module_info['alias']['result'] && isset($array_op[1]) && $array_op[1] == Url::getKQLCNDT() && empty($array_op[2])
) {
    $listresult_page = true;
    $op = $module_info['alias']['listresult'];

//Set url riêng cho thông báo mời sơ tuyển nhà thầu/nhà đầu tư
} elseif ($op == $module_info['alias']['prequalification'] && empty($array_op) && !$nv_Request->isset_request('nocache', 'post,get')
    || $op == $module_info['alias']['prequalification'] && isset($array_op[1]) && $array_op[1] == Url::getNT() && empty($array_op[2])
    || $op == $module_info['alias']['prequalification'] && isset($array_op[1]) && $array_op[1] == Url::getNDT() && empty($array_op[2])
) {
    $listprequalification_page = true;
    $op = $module_info['alias']['listprequalification'];

//Set url riêng cho kế hoạch lựa chọn nhà thầu/nhà đầu tư
} elseif ($op == $module_info['alias']['plans'] && empty($array_op) && !$nv_Request->isset_request('nocache', 'post,get')
    || $op == $module_info['alias']['plans'] && isset($array_op[1]) && $array_op[1] == Url::getKHLCNT() && empty($array_op[2])
    || $op == $module_info['alias']['plans'] && isset($array_op[1]) && $array_op[1] == Url::getKHLCNDT() && empty($array_op[2])
    || $op == $module_info['alias']['plans'] && isset($array_op[1]) && $array_op[1] == Url::getKHLCNT() && isset($array_op[2]) && $array_op[2] == $module_info['alias']['tinh-thanh'] && isset($array_op[3])
    ) {
    $listplan_page = true;
    $op = $module_info['alias']['listplan'];
} elseif ($op == $module_info['alias']['viewopenpq'] && empty($array_op) && !$nv_Request->isset_request('nocache', 'post,get')
    || $op == $module_info['alias']['viewopenpq'] && isset($array_op[1]) && $array_op[1] == Url::getNT() && empty($array_op[2])
    || $op == 'main' && isset($array_op[0]) && isset($array_op[1]) && $array_op[0] == Url::getKQMHSQTNT() && $array_op[1] == Url::getNT()
    || $op == 'main' && isset($array_op[0]) && isset($array_op[1]) && $array_op[0] == Url::getKQMHSQTNT() && $array_op[1] == Url::getNT1())
{
    $op = $module_info['alias']['listopenpq'];
} elseif ($op == 'main' && isset($array_op[0]) && isset($array_op[1]) && $array_op[0] == Url::getMQTNT() && $array_op[1] == Url::getNT1()) {
    $op = $module_info['alias']['listprequalification'];
} elseif ($op == 'main' && isset($array_op[0]) && isset($array_op[1]) && $array_op[0] == Url::getKQMQTNT() && $array_op[1] == Url::getNT1()) {
    $op = $module_info['alias']['listresultpq'];
//Set url riêng cho kế hoạch tổng thể lựa chọn nhà thầu
} elseif ($op == $module_info['alias']['planoverall'] && empty($array_op) && !$nv_Request->isset_request('nocache', 'post,get')
    || $op == $module_info['alias']['planoverall'] && isset($array_op[1]) && $array_op[1] == Url::getKHTTLCNT() && empty($array_op[2])
    || $op == $module_info['alias']['planoverall'] && isset($array_op[1]) && $array_op[1] == Url::getKHTTLCNT() && isset($array_op[2]) && $array_op[2] == $module_info['alias']['tinh-thanh'] && isset($array_op[3])
) {
    $listplanoverall_page = true;
    $op = $module_info['alias']['listplanoverall'];
}



$arr_op = [
    'detail',
    'org',
    'listplan',
    'listprequalification',
    'listresult',
    'listresultpq',
    'open',
    'plans',
    'kehoach',
    'prequalification',
    'moisotuyen',
    'project',
    'projectdetail',
    'duanmoicongbo',
    'result',
    'ketqua',
    'resultpq',
    'ketquasotuyen',
    'solicitor',
    'view',
    'thongbao',
    'viewopen',
    'ketquamothau',
    'ketquamosotuyen',
    'listopenpq'
];

$arr_title_province = [
    815,//Tp cần thơ
    501,//đà nẵng
    101,//hà nội
    103,//hải phòng
    701//HCM
];

// lẤY PROVINCE
$sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province ORDER BY title ASC";
$province_list = $nv_Cache->db($sql, 'id', 'location_bidding');
$province_list[0] = array(
    'id' => 0,
    'title' => $nv_Lang->getModule('no_title'),
    'alias' => 'Chua-phan-loai'
);
// lẤY District
$sql = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_district ORDER BY title ASC";
$district_list = $nv_Cache->db($sql, 'id', 'location_bidding');
$district_list[0] = array(
    'id' => 0,
    'title' => $nv_Lang->getModule('no_title'),
    'alias' => 'Chua-phan-loai'
);

// $sql = "SELECT id, parent_id, parent_id, title_" . NV_LANG_DATA . " as title , alias_" . NV_LANG_DATA . " as alias FROM " . BID_PREFIX_GLOBAL . "_phanmuc WHERE parent_id > 0 AND status = 1";
// $phanmuc_list = $nv_Cache->db($sql, 'id', NV_LANG_DATA . '_phanmuc');
// $phanmuc_list[0] = array(
//     'id' => 0,
//     'title' => $nv_Lang->getModule('no_title'),
//     'alias' => $nv_Lang->getModule('alias_chua_phan_loai')
// );

$cache = $nv_Cache->getItem($module_name, NV_LANG_DATA . '_phanmuc.cache');
if ($cache !== 'false' && $cache != '') {
    //Nếu đã lưu cache thì đọc luôn
    $phanmuc_list = json_decode($cache, 'true');
} else {
    $sql = "SELECT id, parent_id, title_" . NV_LANG_DATA . " as title , alias_" . NV_LANG_DATA . " as alias FROM " . BID_PREFIX_GLOBAL . "_phanmuc WHERE status = 1";
    $result_phanmuc = $db->query($sql);
    while ($phan_muc = $result_phanmuc->fetch()) {
        $phan_muc['link'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['phan-muc'] . '/' . $phan_muc['alias'], true);
        if ($phan_muc['parent_id'] == 0) {
            foreach ($phan_muc as $k => $v) {
                $phanmuc_list[$phan_muc['id']][$k] = $v;
            }
        } else {
            $phanmuc_list[$phan_muc['parent_id']]['children'][$phan_muc['id']] = $phan_muc;
        }
    }
    $phanmuc_list[0] = array(
        'id' => 0,
        'title' => $nv_Lang->getModule('no_title'),
        'alias' => $nv_Lang->getModule('alias_chua_phan_loai'),
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['phan-muc'] . '/' . $nv_Lang->getModule('alias_chua_phan_loai'), true)
    );
    $nv_Cache->setItem($module_name, NV_LANG_DATA . '_phanmuc.cache', json_encode($phanmuc_list), 600);
}
$phanmuc_id_list = [];
foreach ($phanmuc_list as $p) {
    if (empty($p['id'])) {
        continue;
    }
    $phanmuc_id_list[] = $p['id'];
    if (!empty($p['children'])) {
        foreach ($p['children'] as $child) {
            $phanmuc_id_list[] = $child['id'];
        }
    }
}
// ward
$sql_ward = "SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_ward";
$ward_list = $nv_Cache->db($sql_ward, 'id', 'location');

if ((NV_CURRENTTIME >= $close_time_dauthau and NV_CURRENTTIME <= $open_maunal_time_dauthau and ($type_user == 0 or $type_user == 1)) and !defined('NV_IS_AJAX') and in_array($op, $arr_op) and !$client_info['is_bot']) {
    $contents = "<div class=\"alert alert-warning\">Từ ngày 26/04/2021, Phần mềm DauThau.info ngừng quét dữ liệu vào Hệ thống mạng đấu thầu quốc gia theo yêu cầu của Bộ KH&ĐT và chuyển sang nhập liệu dữ liệu thủ công. Quá trình thay đổi này dự kiến mất vài ngày. <br/>
Kính mong quý khách thông cảm vì sự bất tiện này!</div>";
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

require NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

// Agency types
$agencyTypes = [
    0 => $nv_Lang->getModule('all'),
    1 => $nv_Lang->getModule('ministry_sector'),
    2 => $nv_Lang->getModule('corporations'),
    3 => $nv_Lang->getModule('local')
];

// Projects group types
$projectGroupTypes = [
    0 => $nv_Lang->getModule('all'),
    'A' => $nv_Lang->getModule('dm_group_da_2'),
    'B' => $nv_Lang->getModule('dm_group_da_3'),
    'C' => $nv_Lang->getModule('dm_group_da_4'),
    'KHAC' => $nv_Lang->getModule('other')
];

// Projects group types
$listBiddingTypes = [
    0 => $nv_Lang->getModule('bidding_offline'),
    1 => $nv_Lang->getModule('bidding_online')
];

// status solicitor
$status_title = [
    1 => $nv_Lang->getModule('status_title_1'),
    2 => $nv_Lang->getModule('status_title_2'),
    3 => $nv_Lang->getModule('status_title_3')
];

// type_org solicitor
$type_org_title = [
    1 => $nv_Lang->getModule('type_org_big'),
    2 => $nv_Lang->getModule('type_org_medium'),
    3 => $nv_Lang->getModule('type_org_small')
];

// gọi hàm xử lý submit crawl để lấy text confirm
Share::submitConfirmCrawl();


function nv_substr_clean($string, $mode = 'lr')
{
    $strlen = nv_strlen($string);
    $pos_bg = nv_strpos($string, ' ') + 1;
    $pos_en = nv_strrpos($string, ' ');
    if ($mode == 'l') {
        $string = '...' . nv_substr($string, $pos_bg, $strlen - $pos_bg);
    } elseif ($mode == 'r') {
        $string = nv_substr($string, 0, $strlen - $pos_en) . '...';
    } elseif ($mode == 'lr') {
        $string = '...' . nv_substr($string, $pos_bg, $pos_en - $pos_bg) . '...';
    }

    return $string;
}

function nv_custom_insert($data, $table)
{
    global $db;
    $id = 0;
    if (!empty($data)) {
        foreach ($data as $key => $value) {
            $arrayKey[] = $key;
            $arrayInsert[] = $db->quote($value);
        }
        try {
            $sqlInsert = "INSERT INTO $table (" . implode(",", $arrayKey) . ") VALUES ( " . implode(",", $arrayInsert) . " )";

            $db->query($sqlInsert);
            $id = $db->lastInsertId();
        } catch (PDOException $e) {
            trigger_error($e);
            $id = -1;
        }
    }
    return $id;
}

function BoldKeywordInStr($str, $keyword, $logic)
{
    $str = nv_br2nl($str);
    $str = nv_nl2br($str, ' ');
    $str = nv_unhtmlspecialchars(strip_tags(trim($str)));

    if (empty($keyword)) {
        return nv_clean60($str, 300);
    }

    if ($logic == 'AND') {
        $array_keyword = array(
            $keyword,
            nv_EncString($keyword)
        );
    } else {
        $keyword .= ' ' . nv_EncString($keyword);
        $array_keyword = explode(' ', $keyword);
        $array_keyword = array_unique($array_keyword);
    }
    $pos = false;
    $pattern = [];
    foreach ($array_keyword as $k) {
        $_k = function_exists('searchPatternByLang') ? searchPatternByLang(nv_preg_quote($k)) : nv_preg_quote($k);
        $pattern[] = $_k;
        if (!$pos and preg_match('/^(.*?)' . $_k . '/isu', $str, $matches)) {
            $strlen = nv_strlen($str);
            $kstrlen = nv_strlen($k);
            $residual = $strlen - 300;
            if ($residual > 0) {
                $lstrlen = nv_strlen($matches[1]);
                $rstrlen = $strlen - $lstrlen - $kstrlen;

                $medium = round((300 - $kstrlen) / 2);
                if ($lstrlen <= $medium) {
                    $str = nv_clean60($str, 300);
                } elseif ($rstrlen <= $medium) {
                    $str = nv_substr($str, $residual, 300);
                    $str = nv_substr_clean($str, 'l');
                } else {
                    $str = nv_substr($str, $lstrlen - $medium, $strlen - $lstrlen + $medium);
                    $str = nv_substr($str, 0, 300);
                    $str = nv_substr_clean($str, 'lr');
                }
            }

            $pos = true;
        }
    }
    if (!$pos) {
        return nv_clean60($str, 300);
    }

    $pattern = '/(' . implode('|', $pattern) . ')/isu';

    return preg_replace($pattern, '<span class="keyword">$1</span>', $str);
}

/**
 * Cập nhật danh sách bộ lọc trong trường filter_ids của bảng custom
 *
 * @param integer $userid
 * @since 14/11/2019
 */
function DT_UpdateCustomFilters($userid)
{
    global $db, $module_data;

    /*
     * Lấy các bộ lọc đang hiệu lực của thành viên
     * và nhóm theo mảng VIP
     */
    $sql = "SELECT * FROM " . BID_PREFIX_GLOBAL . "_filter WHERE userid=" . $userid . " AND vip_use>0";
    $result = $db->query($sql);

    $array_filters = [];
    $array_filter_titles = [];
    while ($row = $result->fetch()) {
        if (!isset($array_filters[$row['vip_use']])) {
            $array_filters[$row['vip_use']] = [];
            $array_filter_titles[$row['vip_use']] = [];
        }
        $array_filters[$row['vip_use']][] = $row['id'];
        $array_filter_titles[$row['vip_use']][] = $row['title'];
        if ($row['vip_use'] == 1 or $row['vip_use'] == 2) {
            if (!isset($array_filters[99])) {
                $array_filters[99] = [];
                $array_filter_titles[99] = [];
            }
            $array_filters[99][] = $row['id'];
            $array_filter_titles[99][] = $row['title'];
        }
    }

    // Cập nhật lại bảng custom
    $sql = "UPDATE " . BID_PREFIX_GLOBAL . "_customs SET
        updatetime=" . NV_CURRENTTIME . ",
        filter_ids='',
        filter_titles=''
    WHERE user_id=" . $userid;
    $db->query($sql);

    foreach ($array_filters as $vip => $filter_ids) {
        $sql = "UPDATE " . BID_PREFIX_GLOBAL . "_customs SET
            updatetime=" . NV_CURRENTTIME . ",
            filter_ids='" . implode(',', $filter_ids) . "',
            filter_titles=" . $db->quote(json_encode($array_filter_titles[$vip])) . "
        WHERE user_id=" . $userid . " AND vip=" . $vip;
        $db->query($sql);
    }
}

function hsmtFiles_reUpdate($id, $download_priority)
{
    global $db;
    $tamp_sql = "";
    if ($download_priority > 0) {
        $tamp_sql = ", download_priority = download_priority + 1, time_ho_so = UNIX_TIMESTAMP()";
    }
    $db->query("UPDATE " . BIDDING_ROW_TABLE . " SET get_ho_so = 1 " . $tamp_sql . " WHERE id=" . $id);
    $db->query("UPDATE " . BIDDING_DETAIL_TABLE . " SET ho_so_point = '', ho_so_filesize = '0', quyet_dinh_point = '', quyet_dinh_filesize = '0' WHERE id=" . $id);
}

/**
 *
 * @param int $userid
 *            userid=0 sẽ lấy $user_info['userid']
 * @param string $excludes
 *            Danh sách loại trừ, ví dụ: 77,88,99 - phân cách bằng dấu phẩy
 * @return array
 */
function get_VIPList($userid, $excludes = '')
{
    global $db, $user_info;

    if (!defined('NV_IS_USER')) {
        return [];
    }

    $VIPList = [];

    if (!empty($excludes)) {
        $excludes = preg_replace("/[^\d\,]/", "", $excludes);
        $excludes = explode(",", $excludes);
    }
    $_userid = $userid > 0 ? $userid : $user_info['userid'];
    $sql = 'SELECT * FROM ' . BIDDING_CUSTOMS_TABLE . ' WHERE user_id=' . $_userid . ' AND status=1 AND from_time <= ' . NV_CURRENTTIME . ' AND end_time>=' . NV_CURRENTTIME . ' AND prefix_lang = ' . BID_LANG_DATA;
    if (!empty($excludes)) {
        $sql .= ' AND vip NOT IN (' . implode(",", $excludes) . ')';
    }
    $vip = $db->query($sql);
    while ($_row = $vip->fetch()) {
        $VIPList[$_row['vip']] = $_row;
    }

    return $VIPList;
}

function setMySqlRangeTimes($is_elas, $sfrom, $sto, $q, &$error)
{
    global $module_name, $module_config, $client_info, $nv_Lang;

    $configs = $module_config[$module_name];

    $_m = [];
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sto, $m)) {
        $sto1 = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
    } elseif (preg_match('/^(20[0-9]{2})([0-9]{2})([0-9]{5})/', $q, $_m) && $_m[2] <= 12) {
        // Cộng thêm 6 tháng giới hạn tìm kiếm
        $_m[2] += 6;
        if ($_m[2] > 12) {
            $_m[2] -= 12;
            ++$_m[1];
        }
        $number = cal_days_in_month(CAL_GREGORIAN, $_m[2], $_m[1]);
        $sto1 = mktime(0, 0, 0, $_m[2], $number, $_m[1]);
    } else {
        $sto1 = NV_CURRENTTIME;
    }
    $sto1 > NV_CURRENTTIME && $sto1 = NV_CURRENTTIME;

    $_m = [];
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $sfrom, $m)) {
        $sfrom1 = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    } else {
        $sfrom1 = 0;
    }
    $sfrom1 > NV_CURRENTTIME && $sfrom1 = NV_CURRENTTIME;
    $sto2 = $sto1 - 3600;
    $sfrom1 > $sto2 && $sfrom1 = $sto2;

    $search_mysql_range = 0;
    if ($is_elas) {
        if (empty($configs['search_mindate']) or !preg_match("/^(\d{2})\/(\d{2})\/(\d{4})/", $configs['search_mindate'])) {
            $configs['search_mindate'] = "31/12/2010";
        }
        $search_mindate = explode("/", $configs['search_mindate']);
        $sfrom_check = mktime(0, 0, 0, $search_mindate[1], $search_mindate[0], $search_mindate[2]);
        if (!defined('NV_IS_USER')) {
            $sfrom_check = strtotime(date("Y-m-d", $sto1) . " -12 month");
        }
    } else {
        $search_mysql_range = intval($configs['search_mysql_range']);
        if ($search_mysql_range < 1)
            $search_mysql_range = 1;
        if (defined('NV_IS_ADMIN'))
            $search_mysql_range = 24;

        $_search_mysql_range = $search_mysql_range == 1 ? "-" . $search_mysql_range . " month" : "-" . $search_mysql_range . " months";
        $sfrom_check = strtotime(date("Y-m-d", $sto1) . $_search_mysql_range);
    }

    if ($sfrom1 <= $sfrom_check) {
        $sfrom1 = $sfrom_check;
        if ($is_elas and !defined('NV_IS_USER')) {
            $link_login = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
            isset($_GET['sfrom']) && $error[] = sprintf($nv_Lang->getModule('notification_tb'), 12, nv_date("d/m/Y", $sfrom1), nv_date("d/m/Y", $sto1), $link_login);
        } else {
            isset($_GET['sfrom']) && $search_mysql_range && $error[] = sprintf($nv_Lang->getModule('sql_rangetime_error'), $search_mysql_range, nv_date("d/m/Y", $sfrom1), nv_date("d/m/Y", $sto1));
        }
    }

    return [
        $sfrom1,
        $sto1
    ];
}

if ($nv_Request->isset_request('copy_userid', 'get,post')) {
    $res = [
        'phone' => '',
        'contact_phone' => '',
        'name' => '',
        'email' => '',
        'address_bill' => '',
        'tax' => ''
    ];
    if (defined('NV_IS_USER')) {
        $info = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . '_info WHERE userid=' . $user_info['userid'])->fetch();
        $res['phone'] = str_replace("'", "", $info['phone']);
        $res['contact_phone'] = str_replace("'", "", $info['phone']);
        $res['name'] = $user_info['full_name'] != '' ? $user_info['full_name'] : $user_info['username'];
        $res['email'] = $user_info['email'];
        $res['tax'] = $info['mst'];
        $res['address_bill'] = $user_info['email'];
    }
    nv_jsonOutput($res);
}

/**
 * Thanh tiến trình trong các phần KHLCNT, TBMT, QBMT, KQST, KQLCNT ...
 * chưa thống nhất, hàm chuyển để để thống nhất dữ liệu
 * old progress
 * $array_data_process = array(
 * 'is_online' => 1,
 * 'is_pre' => 1,
 * 'khlcnt' => '',
 * 'khlcnt_url' => '',
 * 'tbmt' => '',
 * 'tbmt_url' => '',
 * 'kqmt' => 'hidden',
 * 'kqmt_url' => '',
 * 'kqlcnt' => '',
 * 'kqlcnt_url' => '',
 * 'tbmst' => '',
 * 'tbmst_url' => '',
 * 'kqst' => '',
 * 'kqst_url' => '',
 * 'width' => 20
 * );
 * new progress
 * $array_data_process_new = [];
 * $array_data_process_new['khlcnt'] = [
 * 'act' => !(empty($array_data_process['khlcnt']) || $array_data_process['khlcnt'] == 'hidden'),
 * 'is_current' => false,
 * 'url' => $array_data_process['khlcnt_url'],
 * 'classes' => 'khlcnt',
 * 'title' => $nv_Lang->getModule('list_plan')
 * ];
 */
function transform_progress($array_data_process, $_invest_field = '')
{
    global $nv_Lang;
    $array_data_process_new = [];
    isset($array_data_process['is_online']) && $array_data_process_new['is_online'] = $array_data_process['is_online'];
    isset($array_data_process['is_pre']) && $array_data_process_new['is_pre'] = $array_data_process['is_pre'];
    $array_data_process_new['dapt'] = [
        'act' => !(empty($array_data_process['dapt']) || $array_data_process['dapt'] == 'hidden'),
        'is_current' => false,
        'url' => $array_data_process['dapt_url'] ?? '',
        'classes' => 'devproject' . ' ' . ($array_data_process['dapt'] ?? ''),
        'title' => $nv_Lang->getModule('project')
    ];
    $array_data_process_new['khttlcnt'] = [
        'act' => !(empty($array_data_process['khttlcnt']) || $array_data_process['khttlcnt'] == 'hidden'),
        'is_current' => false,
        'url' => $array_data_process['khttlcnt_url'] ?? '',
        'classes' => 'khttlcnt' . ' ' . ($array_data_process['khttlcnt'] ?? ''),
        'title' => $nv_Lang->getModule('pagetitle_khttlcnt')
    ];
    $array_data_process_new['khlcnt'] = [
        'act' => !(empty($array_data_process['khlcnt']) || $array_data_process['khlcnt'] == 'hidden'),
        'is_current' => false,
        'url' => $array_data_process['khlcnt_url'] ?? '',
        'classes' => 'khlcnt' . ' ' . ($array_data_process['khlcnt'] ?? ''),
        'title' => $nv_Lang->getModule('list_plan')
    ];

    $array_data_process_new['tbmst'] = [
        'act' => !(empty($array_data_process['tbmst']) || $array_data_process['tbmst'] == 'hidden'),
        'is_current' => false,
        'url' => $array_data_process['tbmst_url'] ?? '',
        'classes' => 'tbmst' . ' ' . ($array_data_process['tbmst'] ?? ''),
        'title' => $_invest_field == $nv_Lang->getModule('field_3') ? $nv_Lang->getModule('prequalification_qt') : $nv_Lang->getModule('prequalification_title')
    ];

    if (isset($array_data_process['kqmst'])) {
        $array_data_process_new['kqmst'] = [
            'act' => !(empty($array_data_process['kqmst']) || $array_data_process['kqmst'] == 'hidden'),
            'is_current' => false,
            'url' => $array_data_process['kqmst_url'] ?? '',
            'classes' => 'kqmt' . ' ' . ($array_data_process['kqmst'] ?? ''),
            'title' => $_invest_field == $nv_Lang->getModule('field_3') ? $nv_Lang->getModule('rs_open_prequalification_qt') : $nv_Lang->getModule('rs_open_prequalification')
        ];
    }
    $array_data_process_new['kqst'] = [
        'act' => !(empty($array_data_process['kqst']) || $array_data_process['kqst'] == 'hidden'),
        'is_current' => false,
        'url' => $array_data_process['kqst_url'] ?? '',
        'classes' => 'kqst' . ' ' . ($array_data_process['kqst'] ?? ''),
        'title' => $_invest_field == $nv_Lang->getModule('field_3') ? $nv_Lang->getModule('result_prequalification_title_qt') : $nv_Lang->getModule('result_prequalification_title')
    ];
    if (isset($array_data_process['tbmt'])) {
        $array_data_process_new['tbmt'] = [
            'act' => !(empty($array_data_process['tbmt']) || $array_data_process['tbmt'] == 'hidden'),
            'is_current' => false,
            'url' => $array_data_process['tbmt_url'] ?? '',
            'classes' => 'tbmt' . ' ' . ($array_data_process['tbmt'] ?? ''),
            'title' => $nv_Lang->getModule('tender_title')
        ];
    }
    if (isset($array_data_process['kqmt'])) {
        $array_data_process_new['kqmt'] = [
            'act' => !(empty($array_data_process['kqmt']) || $array_data_process['kqmt'] == 'hidden'),
            'is_current' => false,
            'url' => $array_data_process['kqmt_url'] ?? '',
            'classes' => 'kqmt' . ' ' . ($array_data_process['kqmt'] ?? ''),
            'title' => $nv_Lang->getModule('listopen')
        ];
    }
    if (isset($array_data_process['roomcgtt'])) {
        $array_data_process_new['roomcgtt'] = [
            'act' => !(empty($array_data_process['roomcgtt']) || $array_data_process['roomcgtt'] == 'hidden'),
            'is_current' => false,
            'url' => $array_data_process['roomcgtt_url'] ?? '',
            'classes' => 'khlcnt' . ' ' . ($array_data_process['roomcgtt'] ?? ''),
            'title' => $nv_Lang->getModule('roomreoffer')
        ];
    }
    if (isset($array_data_process['kqcgtt'])) {
        $array_data_process_new['kqcgtt'] = [
            'act' => !(empty($array_data_process['kqcgtt']) || $array_data_process['kqcgtt'] == 'hidden'),
            'is_current' => false,
            'url' => $array_data_process['kqcgtt_url'] ?? '',
            'classes' => 'kqmt' . ' ' . ($array_data_process['kqcgtt'] ?? ''),
            'title' => $nv_Lang->getModule('listreoffer')
        ];
    }
    $array_data_process_new['kqlcnt'] = [
        'act' => !(empty($array_data_process['kqlcnt']) || $array_data_process['kqlcnt'] == 'hidden'),
        'is_current' => false,
        'url' => $array_data_process['kqlcnt_url'] ?? '',
        'classes' => 'kqlcnt' . ' ' . ($array_data_process['kqlcnt'] ?? ''),
        'title' => $nv_Lang->getModule('kq_lcnt')
    ];
    return $array_data_process_new;
}

// / hết hạn các bộ lọc mua điểm
function nvUpdateFilterExpired()
{
    global $db, $module_data;

    $sql = 'SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE dateexpired < ' . NV_CURRENTTIME . ' ORDER BY dateexpired DESC';
    $point_mail_result = $db->query($sql);
    while ($_row = $point_mail_result->fetch()) {
        $query = 'UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET status=2 WHERE id=' . $_row['filterid'];
        $db->query($query);
    }
}

function get_log_crawls($id, $key)
{
    /*
     * Tham số key
     * KQMT: Kết quả mở thầu
     * KHLCNT: Kế hoạch lựa chọn nhà thầu
     * TBMT: Thông báo mời thầu
     * KQLCNT: Kết quả lựa chọn nhà thầu
     * MST: Mời sơ tuyển
     * KQST: Kết quả sơ tuyển
     * KQMST: Kết quả mở sơ tuyển
     * DAMCBDT: Dự án mới công bố đầu tư
     * KQSTDT: Kết quả sơ tuyển đầu tư
     * KQLCDT: Kết quả lựa chọn nhà đầu tư
     * MSTDT: Mời sơ tuyển đầu tư
     * TBMTDT: Thông báo mời thầu đầu tư
     * KQCGTT: Kết quả chào giá trực tuyến rút gọn
     */
    global $db, $arr_unset, $nv_Lang;
    $data_log_crawls = [];
    if (defined('NV_IS_ADMIN')) {
        $key = strtolower($key);
        $param_obj_id = ($key == 'kqmt') ? PDO::PARAM_STR : PDO::PARAM_INT;
        $sth = $db->prepare("SELECT * FROM nv4_vi_logs_crawls_" . $key . " WHERE obj_id = :obj_id ORDER BY log_time_new DESC LIMIT 30");
        $sth->bindParam(':obj_id', $id, $param_obj_id);
        $sth->execute();
        while ($row = $sth->fetch()) {
            $tmp_log = json_decode($row['log_data'], true);
            !is_array($tmp_log) && $tmp_log = [];
            foreach ($arr_unset as $key => $value) {
                unset($tmp_log[$value]);
            }
            empty($tmp_log) && $tmp_log = ['new' => ['no_change' => 'Không có thay đổi']];
            // Nếu không có thay đổi sẽ vào if này
            if (isset($tmp_log['new']['no_change'])) {
                $no_change = $tmp_log['new']['no_change'];
                unset($tmp_log['old']);
                unset($tmp_log['new']);
                $tmp_log['log_time_new'] = nv_date('H:i:s d/m/Y', $row['log_time_new']);
                $tmp_log['log_time_old'] = nv_date('H:i:s d/m/Y', $row['log_time_old']);
                $tmp_log['no_change'] = $no_change;
                $data_log_crawls[] = $tmp_log;
            } else {
                if (!empty($tmp_log)) {
                    if (isset($tmp_log['log_time_old']) and isset($tmp_log['log_time_new'])) {
                        if ($tmp_log['log_time_old'] >= $tmp_log['log_time_new']) {
                            $tmp_log['log_time_old'] = $tmp_log['log_time_new'];
                        }
                    }
                    $tmp_log['log_time_new'] = nv_date('H:i d/m/Y', $row['log_time_new']);
                    $tmp_log['log_time_old'] = nv_date('H:i d/m/Y', $row['log_time_old']);
                    $tmp_log = nv_str_replace_recursion(['<html>', '</html>', '<body>', '</body>'], ['', '', '', ''], $tmp_log);
                    $data_log_crawls[] = $tmp_log;
                }
            }
        }
    }
    return $data_log_crawls;
}

function PopupLogin($mess, $page_url = '')
{
    global $module_info, $global_config, $module_name, $op, $nv_Lang;
    $xtpl = new XTemplate('popup_login.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/bidding');
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('POPUP_LOGIN', $mess);
    if ($page_url != '') {
        $xtpl->assign('PAGE_URL', nv_redirect_encrypt(urlRewriteWithDomain($page_url, NV_MY_DOMAIN)));
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function f_date($data)
{
    $data['old'] = !empty($data['old']) ? nv_date('H:i d/m/Y', $data['old']) : '';
    $data['new'] = !empty($data['new']) ? nv_date('H:i d/m/Y', $data['new']) : '';
    return $data;
}

function f_solicitor($data)
{
    $_solicitor_format = [];
    if (!empty($data)) {
        $_solicitor_format['old'] = g_solicitor($data['old']);
        $_solicitor_format['new'] = g_solicitor($data['new']);
    }
    return $_solicitor_format;
}

function g_solicitor($id)
{
    global $db;
    $result = "";
    if (!empty($id)) {
        $result = $db->query("SELECT title FROM " . BID_PREFIX_GLOBAL . "_solicitor WHERE id = " . $db->quote($id))
            ->fetch();
    }
    if (!empty($result)) {
        return $result['title'];
    }
    return $result;
}

function f_linhvuc($data)
{
    global $nv_Lang;

    $arr_field = array(
        1 => $nv_Lang->getModule('field_1'),
        2 => $nv_Lang->getModule('field_2'),
        3 => $nv_Lang->getModule('field_3'),
        4 => $nv_Lang->getModule('field_4'),
        5 => $nv_Lang->getModule('field_5'),
        // 6 => $nv_Lang->getModule('field_6')
        7 => $nv_Lang->getModule('field_7'),
        8 => $nv_Lang->getModule('prequalification_2')
    );
    $_linhvuc_format = [];
    if (!empty($data)) {
        $_linhvuc_format['old'] = isset($arr_field[$data['old']]) ? $arr_field[$data['old']] : "";
        $_linhvuc_format['new'] = isset($arr_field[$data['new']]) ? $arr_field[$data['new']] : "";
    }
    return $_linhvuc_format;
}

function f_hinhthuc($data)
{
    global $nv_Lang;

    $arr_field = array(
        0 => $nv_Lang->getModule('type_lc_6'),
        1 => $nv_Lang->getModule('cat_filters_2')
    );
    if (!empty($data)) {
        $data['old'] = isset($arr_field[$data['old']]) ? $arr_field[$data['old']] : "";
        $data['new'] = isset($arr_field[$data['new']]) ? $arr_field[$data['new']] : "";
    }
    return $data;
}

function f_status_solicitor($data)
{
    global $status_title;

    if (!empty($data)) {
        $data['old'] = !empty($status_title[$data['old']]) ? $status_title[$data['old']] : (!empty($data['old']) ? $data['old'] : '');
        $data['new'] = !empty($status_title[$data['new']]) ? $status_title[$data['new']] : (!empty($data['new']) ? $data['new'] : '');
    }
    return $data;
}

function f_solicitor_type($data)
{
    global $business_type;

    if (!empty($data)) {
        $data['old'] = !empty($business_type[$data['old']]['title']) ? $business_type[$data['old']]['title'] : (!empty($data['old']) ? $data['old'] : '');
        $data['new'] = !empty($business_type[$data['new']]['title']) ? $business_type[$data['new']]['title'] : (!empty($data['new']) ? $data['new'] : '');
    }
    return $data;
}

function f_nation_solicitor($data)
{
    global $arr_nation;

    if (!empty($data)) {
        $data['old'] = !empty($arr_nation[$data['old']]['name']) ? $arr_nation[$data['old']]['name'] : (!empty($data['old']) ? $data['old'] : '');
        $data['new'] = !empty($arr_nation[$data['new']]['name']) ? $arr_nation[$data['new']]['name'] : (!empty($data['new']) ? $data['new'] : '');
    }
    return $data;
}

function f_rep_position_solicitor($data)
{
    if (!empty($data)) {
        $_rep_position_solicitor['old'] = g_rep_position_solicitor($data['old']);
        $_rep_position_solicitor['old'] = !empty($_rep_position_solicitor['old']) ? $_rep_position_solicitor['old'] : (!empty($data['old']) ? $data['old'] : '');
        $_rep_position_solicitor['new'] = g_rep_position_solicitor($data['new']);
        $_rep_position_solicitor['new'] = !empty($_rep_position_solicitor['new']) ? $_rep_position_solicitor['new'] : (!empty($data['new']) ? $data['new'] : '');
    }
    return $_rep_position_solicitor;
}

function g_rep_position_solicitor($id)
{
    global $db;
    $result = "";
    if (!empty($id)) {
        $result = $db->query("SELECT title FROM " . NV_PREFIXLANG . "_rep_position WHERE id = " . $db->quote($id))
            ->fetch();
    }
    if (!empty($result)) {
        return $result['title'];
    }
    return $result;
}

function f_management_agency_solicitor($data)
{
    global $management_agency_list;
    if (!empty($data)) {
        $data['old'] = !empty($management_agency_list[$data['old']]['title']) ? $management_agency_list[$data['old']]['title'] : (!empty($data['old']) ? $data['old'] : '');
        $data['new'] = !empty($management_agency_list[$data['new']]['title']) ? $management_agency_list[$data['new']]['title'] : (!empty($data['new']) ? $data['new'] : '');
    }
    return $data;
}

function f_directly_under_solicitor($data)
{
    global $directly_under;
    if (!empty($data)) {
        $data['old'] = !empty($directly_under[$data['old']]['title']) ? $directly_under[$data['old']]['title'] : (!empty($data['old']) ? $data['old'] : '');
        $data['new'] = !empty($directly_under[$data['new']]['title']) ? $directly_under[$data['new']]['title'] : (!empty($data['new']) ? $data['new'] : '');
    }
    return $data;
}

function f_type_org_solicitor($data)
{
    global $type_org_title;

    if (!empty($data)) {
        $data['old'] = !empty($type_org_title[$data['old']]) ? $type_org_title[$data['old']] : (!empty($data['old']) ? $data['old'] : '');
        $data['new'] = !empty($type_org_title[$data['new']]) ? $type_org_title[$data['new']] : (!empty($data['new']) ? $data['new'] : '');
    }
    return $data;
}

function f_province($data)
{
    $_location_format = [];
    if (!empty($data)) {
        $_location_format['old'] = g_province($data['old']);
        $_location_format['new'] = g_province($data['new']);
    }
    return $_location_format;
}

function g_province($id)
{
    global $db;
    $result = "";
    if (!empty($id)) {
        $result = $db->query("SELECT title FROM " . NV_PREFIXLANG . "_location_province WHERE id = " . $db->quote($id))
            ->fetch();
    }
    if (!empty($result)) {
        return $result['title'];
    }
    return $result;
}

function update_url($result_data)
{
    global $dbcr, $nv_Lang;

    $trang_thai = array();
    $trang_thai['1'] = $nv_Lang->getModule('kqmt_status_1');
    $trang_thai['2'] = $nv_Lang->getModule('kqmt_status_2');
    $trang_thai['3'] = $nv_Lang->getModule('kqmt_status_3');
    $trang_thai['4'] = $nv_Lang->getModule('kqmt_status_4');
    $trang_thai['5'] = $nv_Lang->getModule('kqmt_status_5');
    $trang_thai['6'] = $nv_Lang->getModule('kqmt_status_6');
    $trang_thai['7'] = $nv_Lang->getModule('kqmt_status_7');

    $keysearch = explode('-', $result_data['so_tbmt']);
    if ($result_data['id_loai'] == 1) {
        $url = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_MPV_GCQ412?gonggo_num=' . $keysearch[0];
    } else if ($result_data['id_loai'] == 2) {
        $url = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_SSV_GCQ402?gonggo_num=' . $keysearch[0];
    } else if ($result_data['id_loai'] == 3) {
        $url = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_YYV_GCQ412?gonggo_num=' . $keysearch[0];
    } else if ($result_data['id_loai'] == 4) {
        $url = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_NCJ_GCQ412?gonggo_num=' . $keysearch[0];
    } else if ($result_data['id_loai'] == 5) {
        $url = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_MPV_VTT_GCQ412?gonggo_num=' . $keysearch[0];
    }
    $html = geturlhtml($url);

    // Đọc dữ liệu html
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    if (empty($html) or !$dom->loadHTML($html)) {
        return '';
    }
    $xpath = new DOMXPath($dom);
    $url_detail = '';
    // lay thong tin goi thau
    $nodeList = $xpath->query('//table[@class="tr"]/tr');
    foreach ($nodeList as $node) {
        $_html = DOMinnerHTML($node);
        $dom_i = new DOMDocument();

        if ($dom_i->loadHTML($_html)) {
            $xpath_i = new DOMXPath($dom_i);
            $nodeLis_i = $xpath_i->query('//td');
            if ($nodeLis_i->length == 7) {
                // kiem tra neu la TBMT thi lay ket qua
                if (preg_match("/([0-9])+\-([0-9]{2})/", strip_tags($nodeLis_i[1]->nodeValue), $m)) {
                    $contents_array = array();
                    $doc = new DOMDocument();
                    if (!empty($nodeLis_i[6])) {
                        if ($doc->loadHTML(DOMinnerHTML($nodeLis_i[6]))) {
                            $xpath = new DOMXPath($doc);
                            $entries = $xpath->query("//input");

                            foreach ($entries as $entry) {
                                $contents_array['trang_thai'] = $entry->getAttribute("onclick");
                                $_trang_thai = array_search(trim($entry->getAttribute("value")), $trang_thai);
                            }
                        }
                    } else {
                        $_trang_thai = 6;
                    }

                    if ($_trang_thai != $result_data['trang_thai']) {

                        $contents_array['ben_moi_thau'] = strip_tags(trim(html_entity_decode($nodeLis_i[3]->nodeValue, ENT_QUOTES, 'UTF-8')));
                        $contents_array['thoi_diem_mo_thau'] = strip_tags(trim(html_entity_decode($nodeLis_i[4]->nodeValue, ENT_QUOTES, 'UTF-8')));
                        $contents_array['so_luong_nha_thau'] = strip_tags(trim(html_entity_decode($nodeLis_i[5]->nodeValue, ENT_QUOTES, 'UTF-8')));

                        if (preg_match('/([0-9]{1,2}+)\/([0-9]{1,2}+)\/([0-9]{4}+)\s([0-9]{1,2}+)\:([0-9]{1,2}+)/', $contents_array['thoi_diem_mo_thau'], $m)) {
                            $contents_array['thoi_diem_mo_thau'] = mktime($m[4], $m[5], 0, $m[2], $m[1], $m[3]);
                        }

                        if ($_trang_thai != 6) {
                            if (preg_match_all("/([0-9])+/", $contents_array['trang_thai'], $m)) {
                                if ($_trang_thai == 1) {
                                    $url_detail = 'http://muasamcong.mpi.gov.vn:8082/GC/EP_COJ_GCQ905.jsp?bidNo=' . $m[0][0] . '&bidTurnNo=' . $m[0][1] . '&bidCls=' . $m[0][2] . '&rebidNo=' . $m[0][3];
                                } elseif ($_trang_thai == 2) {
                                    $url_detail = 'http://muasamcong.mpi.gov.vn:8082/webentry/hoso_dxkt_view/show_page?bidNo=' . $m[0][0] . '&bidTurnNo=' . $m[0][1] . '&bidType=' . $m[0][2];
                                } elseif ($_trang_thai == 3) {

                                    $url_detail = 'http://muasamcong.mpi.gov.vn:8082/GC/EP_COJ_GCQ905.jsp?bidNo=' . $m[0][0] . '&bidTurnNo=' . $m[0][1] . '&bidCls=' . $m[0][2] . '&rebidNo=0';
                                } elseif ($_trang_thai == 4) {
                                    $url_detail = 'http://muasamcong.mpi.gov.vn:8082/webentry/bien_ban_mo_thau_tc?bid_no=' . $m[0][0] . '&bid_turn_no=' . $m[0][1] . '&bid_type=' . $m[0][2];
                                } elseif ($_trang_thai == 5) {
                                    $id_loai = $result_data['id_loai'];
                                    if (sizeof($m[0]) >= 4 and $id_loai == 1) {
                                        $url_detail = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_MPV_GCQ413?BidNo=' . $m[0][0] . '&BidTurnNo=' . $m[0][1] . '&RebidNo=' . $m[0][2] . '&sequence=' . $m[0][3] . '';
                                    } elseif (sizeof($m[0]) >= 4 and ($id_loai == 2 or $id_loai == 5)) {
                                        $url_detail = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_SSV_GCQ403?gonggo_num=' . $m[0][0] . '&gonggo_cha=' . $m[0][1] . '&gonggo_ret=' . $m[0][2] . '&sequence=' . $m[0][3];
                                    } elseif (sizeof($m[0]) >= 4 and $id_loai == 3) {
                                        $url_detail = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_YYV_GCQ413?BidNo=' . $m[0][0] . '&BidTurnNo=' . $m[0][1] . '&RebidNo=' . $m[0][2] . '&sequence=' . $m[0][3];
                                    } elseif (sizeof($m[0]) >= 4 and $id_loai == 4) {
                                        $url_detail = 'http://muasamcong.mpi.gov.vn:8082/servlet/GC/EP_NCV_GCQ413?BidNo=' . $m[0][0] . '&BidTurnNo=' . $m[0][1] . '&RebidNo=' . $m[0][2] . '&sequence=' . $m[0][3];
                                    } elseif (sizeof($m[0]) == 2) { // Von Khac
                                        $url_detail = 'http://muasamcong.mpi.gov.vn:8081/webentry_adb/bid_cancel/get_cancel_reason?bidNo=' . $m[0][0] . '&bidTurnNo=' . $m[0][1] . '&allUser=Y';
                                    }
                                }
                            }
                        } else {
                            $url_detail = '';
                        }
                    }
                }
            }
        }
    }

    return $url_detail;
}

$bid_notify_status = [];
$bid_notify_status['01'] = $nv_Lang->getModule('bid_notify_status_1');
$bid_notify_status['02'] = $nv_Lang->getModule('bid_notify_status_2');
$bid_notify_status['03'] = $nv_Lang->getModule('bid_notify_status_3');
$bid_notify_status['04'] = $nv_Lang->getModule('bid_notify_status_4');

$dm_khlcnt = [];
$dm_khlcnt['DTPT'] = $nv_Lang->getModule('dm_khlcnt_1');
$dm_khlcnt['TX'] = $nv_Lang->getModule('dm_khlcnt_2');
$dm_khlcnt['KHAC'] = $nv_Lang->getModule('dm_khlcnt_3');

$dm_htlcnt = [];
$dm_htlcnt['DTRR'] = $nv_Lang->getModule('type_lc_17');
$dm_htlcnt['CHCT'] = $nv_Lang->getModule('type_lc_12');
$dm_htlcnt['CHCTRG'] = $nv_Lang->getModule('type_lc_11');
$dm_htlcnt['DTHC'] = $nv_Lang->getModule('type_lc_16');
$dm_htlcnt['MSTT'] = $nv_Lang->getModule('type_lc_19');
$dm_htlcnt['CDT'] = $nv_Lang->getModule('type_lc_14');
$dm_htlcnt['CDTRG'] = $nv_Lang->getModule('type_lc_13');
$dm_htlcnt['TTH'] = $nv_Lang->getModule('type_lc_23');
$dm_htlcnt['LCNT_DB'] = $nv_Lang->getModule('type_lc_30');
$dm_htlcnt['TVCN'] = $nv_Lang->getModule('type_lc_31');
$dm_htlcnt['TCTVCN'] = $nv_Lang->getModule('type_lc_28');
$dm_htlcnt['DPCT'] = $nv_Lang->getModule('type_lc_32');
$dm_htlcnt['QCBS'] = $nv_Lang->getModule('type_lc_33');
$dm_htlcnt['DPG'] = $nv_Lang->getModule('type_lc_15');
$dm_htlcnt['QBS'] = $nv_Lang->getModule('type_lc_34');
$dm_htlcnt['FBS'] = $nv_Lang->getModule('type_lc_35');
$dm_htlcnt['LCS'] = $nv_Lang->getModule('type_lc_36');
$dm_htlcnt['CQS'] = $nv_Lang->getModule('type_lc_37');
$dm_htlcnt['SSS'] = $nv_Lang->getModule('type_lc_38');
$dm_htlcnt['TGTC'] = $nv_Lang->getModule('type_lc_39');
$dm_htlcnt['NHBD'] = $nv_Lang->getModule('type_lc_40');
$dm_htlcnt['TVCT'] = $nv_Lang->getModule('type_lc_41');
$dm_htlcnt['TGTHCD'] = $nv_Lang->getModule('type_lc_20');
$dm_htlcnt['CGTTRG'] = $nv_Lang->getModule('type_lc_42');

$dm_ptlcnt = [];
$dm_ptlcnt['1_MTHS'] = $nv_Lang->getModule('type_lc_7');
$dm_ptlcnt['1_HTHS'] = $nv_Lang->getModule('type_lc_8');
$dm_ptlcnt['2_MTHS'] = $nv_Lang->getModule('type_lc_9');
$dm_ptlcnt['2_HTHS'] = $nv_Lang->getModule('type_lc_10');

$dm_lhdlcnt = [];
$dm_lhdlcnt['DGCD'] = $nv_Lang->getModule('dm_lhdlcnt_1');
$dm_lhdlcnt['DGDC'] = $nv_Lang->getModule('dm_lhdlcnt_2');
$dm_lhdlcnt['TTG'] = $nv_Lang->getModule('dm_lhdlcnt_3');
$dm_lhdlcnt['TG_DGCD'] = $nv_Lang->getModule('dm_lhdlcnt_4');
$dm_lhdlcnt['TG_DGDC'] = $nv_Lang->getModule('dm_lhdlcnt_5');
$dm_lhdlcnt['DGCD_DC'] = $nv_Lang->getModule('dm_lhdlcnt_6');
$dm_lhdlcnt['TG_CD_DC'] = $nv_Lang->getModule('dm_lhdlcnt_7');
$dm_lhdlcnt['TG_CD_TTG'] = $nv_Lang->getModule('dm_lhdlcnt_8');
$dm_lhdlcnt['TG_DC_TTG'] = $nv_Lang->getModule('dm_lhdlcnt_9');
$dm_lhdlcnt['CD_DC_TTG'] = $nv_Lang->getModule('dm_lhdlcnt_10');
$dm_lhdlcnt['TG_CD_DC_TTG'] = $nv_Lang->getModule('dm_lhdlcnt_11');
$dm_lhdlcnt['CD_TTG'] = $nv_Lang->getModule('dm_lhdlcnt_12');
$dm_lhdlcnt['DC_TTG'] = $nv_Lang->getModule('dm_lhdlcnt_13');
$dm_lhdlcnt['TG_TTG'] = $nv_Lang->getModule('dm_lhdlcnt_14');
$dm_lhdlcnt['TGG_CD_DC'] = $nv_Lang->getModule('dm_lhdlcnt_15');
$dm_lhdlcnt['TGG_DGCD'] = $nv_Lang->getModule('dm_lhdlcnt_16');
$dm_lhdlcnt['TGG_DGDC'] = $nv_Lang->getModule('dm_lhdlcnt_17');
$dm_lhdlcnt['TG_CD_DC_TTG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_131');
$dm_lhdlcnt['TG_CD_DC_TTG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_19');
$dm_lhdlcnt['TG_CD_DC_TTG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_20');
$dm_lhdlcnt['TG_CD_DC_TTG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_21');
$dm_lhdlcnt['TG_CD_DC_TTG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_22');
$dm_lhdlcnt['TG_CD_DC_TTG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_23');
$dm_lhdlcnt['TG_CD_DC_TTG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_24');
$dm_lhdlcnt['TG_CD_DC_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_25');
$dm_lhdlcnt['TG_CD_DC_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_26');
$dm_lhdlcnt['TG_CD_DC_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_27');
$dm_lhdlcnt['TG_CD_DC_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_28');
$dm_lhdlcnt['TG_CD_DC_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_29');
$dm_lhdlcnt['TG_CD_DC_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_30');
$dm_lhdlcnt['TG_CD_DC_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_31');
$dm_lhdlcnt['TG_CD_TTG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_32');
$dm_lhdlcnt['TG_CD_TTG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_33');
$dm_lhdlcnt['TG_CD_TTG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_34');
$dm_lhdlcnt['TG_CD_TTG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_35');
$dm_lhdlcnt['TG_CD_TTG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_36');
$dm_lhdlcnt['TG_CD_TTG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_37');
$dm_lhdlcnt['TG_CD_TTG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_38');
$dm_lhdlcnt['TG_CD_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_39');
$dm_lhdlcnt['TG_CD_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_40');
$dm_lhdlcnt['TG_CD_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_41');
$dm_lhdlcnt['TG_CD_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_42');
$dm_lhdlcnt['TG_CD_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_43');
$dm_lhdlcnt['TG_CD_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_44');
$dm_lhdlcnt['TG_CD_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_45');
$dm_lhdlcnt['TG_DC_TTG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_46');
$dm_lhdlcnt['TG_DC_TTG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_47');
$dm_lhdlcnt['TG_DC_TTG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_48');
$dm_lhdlcnt['TG_DC_TTG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_49');
$dm_lhdlcnt['TG_DC_TTG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_50');
$dm_lhdlcnt['TG_DC_TTG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_51');
$dm_lhdlcnt['TG_DC_TTG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_52');
$dm_lhdlcnt['TG_DC_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_53');
$dm_lhdlcnt['TG_DC_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_54');
$dm_lhdlcnt['TG_DC_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_55');
$dm_lhdlcnt['TG_DC_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_56');
$dm_lhdlcnt['TG_DC_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_57');
$dm_lhdlcnt['TG_DC_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_58');
$dm_lhdlcnt['TG_DC_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_59');
$dm_lhdlcnt['TG_TTG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_60');
$dm_lhdlcnt['TG_TTG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_61');
$dm_lhdlcnt['TG_TTG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_62');
$dm_lhdlcnt['TG_TTG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_63');
$dm_lhdlcnt['TG_TTG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_64');
$dm_lhdlcnt['TG_TTG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_65');
$dm_lhdlcnt['TG_TTG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_66');
$dm_lhdlcnt['TG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_67');
$dm_lhdlcnt['TG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_68');
$dm_lhdlcnt['TG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_69');
$dm_lhdlcnt['TG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_70');
$dm_lhdlcnt['TG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_71');
$dm_lhdlcnt['TG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_72');
$dm_lhdlcnt['TG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_73');
$dm_lhdlcnt['DGCD'] = $nv_Lang->getModule('dm_lhdlcnt_74');
$dm_lhdlcnt['CD_DC_TTG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_75');
$dm_lhdlcnt['CD_DC_TTG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_76');
$dm_lhdlcnt['CD_DC_TTG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_77');
$dm_lhdlcnt['CD_DC_TTG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_78');
$dm_lhdlcnt['CD_DC_TTG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_79');
$dm_lhdlcnt['CD_DC_TTG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_80');
$dm_lhdlcnt['CD_DC_TTG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_81');
$dm_lhdlcnt['CD_DC_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_82');
$dm_lhdlcnt['CD_DC_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_83');
$dm_lhdlcnt['CD_DC_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_84');
$dm_lhdlcnt['CD_DC_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_85');
$dm_lhdlcnt['CD_DC_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_86');
$dm_lhdlcnt['CD_DC_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_87');
$dm_lhdlcnt['CD_DC_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_88');
$dm_lhdlcnt['CD_TTG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_89');
$dm_lhdlcnt['CD_TTG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_90');
$dm_lhdlcnt['CD_TTG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_91');
$dm_lhdlcnt['CD_TTG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_92');
$dm_lhdlcnt['CD_TTG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_93');
$dm_lhdlcnt['CD_TTG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_94');
$dm_lhdlcnt['CD_TTG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_95');
$dm_lhdlcnt['CD_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_96');
$dm_lhdlcnt['CD_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_97');
$dm_lhdlcnt['CD_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_98');
$dm_lhdlcnt['CD_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_99');
$dm_lhdlcnt['CD_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_100');
$dm_lhdlcnt['CD_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_101');
$dm_lhdlcnt['CD_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_102');
$dm_lhdlcnt['DC_TTG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_103');
$dm_lhdlcnt['DC_TTG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_104');
$dm_lhdlcnt['DC_TTG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_105');
$dm_lhdlcnt['DC_TTG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_106');
$dm_lhdlcnt['DC_TTG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_107');
$dm_lhdlcnt['DC_TTG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_108');
$dm_lhdlcnt['DC_TTG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_109');
$dm_lhdlcnt['DC_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_110');
$dm_lhdlcnt['DC_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_111');
$dm_lhdlcnt['DC_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_112');
$dm_lhdlcnt['DC_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_113');
$dm_lhdlcnt['DC_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_114');
$dm_lhdlcnt['DC_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_115');
$dm_lhdlcnt['DC_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_116');
$dm_lhdlcnt['TTG_CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_117');
$dm_lhdlcnt['TTG_CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_118');
$dm_lhdlcnt['TTG_CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_119');
$dm_lhdlcnt['TTG_CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_120');
$dm_lhdlcnt['TTG_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_121');
$dm_lhdlcnt['TTG_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_122');
$dm_lhdlcnt['TTG_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_123');
$dm_lhdlcnt['CPCP'] = $nv_Lang->getModule('dm_lhdlcnt_124');
$dm_lhdlcnt['CPCP_KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_125');
$dm_lhdlcnt['CPCP_KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_126');
$dm_lhdlcnt['CPCP_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_127');
$dm_lhdlcnt['KQDR'] = $nv_Lang->getModule('dm_lhdlcnt_128');
$dm_lhdlcnt['KQDR_TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_129');
$dm_lhdlcnt['TLPT'] = $nv_Lang->getModule('dm_lhdlcnt_130');
$dm_lhdlcnt['TG'] = $nv_Lang->getModule('dm_lhdlcnt_188');
$dm_lhdlcnt['KHAC'] = $nv_Lang->getModule('custom_range');

$dm_lvlcnt = [];
$dm_lvlcnt['Other'] = $nv_Lang->getModule('custom_range');
$dm_lvlcnt['HH'] = $nv_Lang->getModule('field_1');
$dm_lvlcnt['XL'] = $nv_Lang->getModule('field_2');
$dm_lvlcnt['PTV'] = $nv_Lang->getModule('field_4');
$dm_lvlcnt['TV'] = $nv_Lang->getModule('field_3');
$dm_lvlcnt['HON_HOP'] = $nv_Lang->getModule('field_5');

$dm_qt = [];
$dm_qt['LDT'] = $nv_Lang->getModule('dm_qt_1');
$dm_qt['ADB'] = $nv_Lang->getModule('dm_qt_2');
$dm_qt['WB'] = $nv_Lang->getModule('dm_qt_3');
$dm_qt['CPTPP'] = $nv_Lang->getModule('dm_qt_4');
$dm_qt['EVFTA'] = $nv_Lang->getModule('dm_qt_5');
$dm_qt['UKFTA'] = $nv_Lang->getModule('dm_qt_6');
$dm_qt['KHAC'] = $nv_Lang->getModule('custom_range');

$dm_group_da = [];
$dm_group_da['DAQG'] = $nv_Lang->getModule('dm_group_da_1');
$dm_group_da['NA'] = $nv_Lang->getModule('dm_group_da_2');
$dm_group_da['NB'] = $nv_Lang->getModule('dm_group_da_3');
$dm_group_da['NC'] = $nv_Lang->getModule('dm_group_da_4');
$dm_group_da['KHAC'] = $nv_Lang->getModule('dm_group_da_5');
$dm_htqlda = getCatogery();

$array_type_hh = [
    'goods_name' => $nv_Lang->getModule('good_name'),
    'sign_product' => $nv_Lang->getModule('code_goods'),
    'description' => $nv_Lang->getModule('desc'),
    'origin' => $nv_Lang->getModule('origin')
];

function getCatogery()
{
    global $nv_Lang;
    $status = '{"status": [
        {
            "code": "BQLDA",
            "name": "' . $nv_Lang->getModule('title_status_name_BQLDA') . '",
            "categoryTypeCode": "DM_HTQLDA"
        },
        {
            "code": "TVQLDA",
            "name": "' . $nv_Lang->getModule('title_status_name_TVQLDA') . '",
            "categoryTypeCode": "DM_HTQLDA"
        },
        {
            "code": "TTQLDA",
            "name": "' . $nv_Lang->getModule('title_status_name_TTQLDA') . '",
            "categoryTypeCode": "DM_HTQLDA"
        },
        {
            "code": "KHAC",
            "name": "' . $nv_Lang->getModule('title_status_name_KHAC') . '",
            "categoryTypeCode": "DM_HTQLDA"
        }
    ]}';
    $status = json_decode($status, true)['status'];
    foreach ($status as $k => $v) {
        $data[$v['code']] = $v;
    }
    return $data;
}

/**
 * getCrawlRequestsHistory()
 * Lấy lịch sử bấm cập nhật lại của người dùng
 *
 * @param $type int
 *          Loại dữ liệu: 'TBMT', 'KQMT', 'KQLCNT', 'KHLCNT', 'BUSINESS', 'SOLICITOR', 'ORG', 'LAS', 'DADTPT', 'YCBG'
 * @param $id int
 *          id của dữ liệu cần tìm
 *
 * @return array
 *
 */
function getCrawlRequestsHistory($type, $id)
{
    global $db, $module_data;

    switch ($type) {
        case 'TBMT':
            $where = 'rowid = ' . $db->quote($id);
            break;
        case 'KQMT':
            $where = 'openid = ' . $db->quote($id);
            break;
        case 'KQLCNT':
            $where = 'resultid = ' . $db->quote($id);
            break;
        case 'KHLCNT':
            $where = 'planid = ' . $db->quote($id);
            break;
        case 'SOLICITOR':
            $where = 'solicitor_id = ' . $db->quote($id);
            break;
        case 'YCBG':
            $where = 'request_quote_id = ' . $db->quote($id);
            break;
        default:
            return [];
    }

    $crawl_request_history = [];
    // Lấy lịch sử bấm cập nhật lại của người dùng
    if (defined('NV_IS_ADMIN')) {
        $result_crawl = $db->query('SELECT id, userid, last_reload FROM ' . BID_PREFIX_GLOBAL . '_update_user WHERE ' . $where . ' ORDER BY last_reload DESC');
        while ($row_crawl = $result_crawl->fetch()) {
            $crawl_request_history[] = $row_crawl;
        }
        $result_crawl->closeCursor();
        if (!empty($crawl_request_history)) {
            $result_user = $db->query('SELECT userid, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', array_map([
                $db,
                'quote'
            ], array_column($crawl_request_history, 'userid'))) . ')');
            while ($row_user = $result_user->fetch()) {
                foreach ($crawl_request_history as $k => $v) {
                    if ($v['userid'] == $row_user['userid']) {
                        $crawl_request_history[$k]['username'] = $row_user['username'];
                    }
                }
            }
        }
    }
    return $crawl_request_history;
}

/**
 * get_lang($lang, $index)
 * Lấy lịch sử bấm cập nhật lại của người dùng
 *
 * @param $lang string
 *          Ngôn ngữ vi/en
 * @param $index string
 *          index của lang muốn lấy
 *
 * @return string|bool
 *
 */
function get_lang ($lang, $index)
{
    global $module_file, $nv_Lang;

    if (!in_array($lang, ['vi', 'en'])) {
        return false;
    }
    if ($lang != NV_LANG_INTERFACE) {
        $nv_Lang->changeLang($lang);
        $nv_Lang->loadModule($module_file, false, true);
    }
    $value = $nv_Lang->getModule($index);
    if ($lang != NV_LANG_INTERFACE) {
        $nv_Lang->changeLang(NV_LANG_INTERFACE);
    }

    return $value;
}

/**
 * num_of_time($num)
 * Đổi số thành dạng số thứ tự tùy thuộc vào ngôn ngữ
 *
 * @param $num int
 *          Số cần chuyển
 *
 * @return string
 *
 */
function num_of_time(int $num) {
    $num = (int) $num;
    if (NV_LANG_DATA == 'vi') {
        return $num;
    } else {
        if ($num % 10 == 1 && $num % 100 != 11) {
            return ($num . '<sup>st</sup>');
        } elseif ($num % 10 == 2 && $num % 100 != 12) {
            return ($num . '<sup>nd</sup>');
        } elseif ($num % 10 == 3 && $num % 100 != 13) {
            return ($num . '<sup>rd</sup>');
        } else {
            return ($num . '<sup>th</sup>');
        }
    }
}

/**
 * @param mixed $month
 * @param mixed $year
 * @return int
 */
function get_last_day($month, $year)
{
    if ($month == 1 or $month == 3 or $month == 5 or $month == 7 or $month == 8 or $month == 10 or $month == 12) {
        return 31;
    } else if ($month == 2) {
        if ($year % 4 == 0 && ($year % 100 != 0 || $year % 400 == 0)) {
            return 29;
        } else {
            return 28;
        }
    } else {
        return 30;
    }
}
global $loai_cong_trinhs;
$loai_cong_trinhs = [
    1 => $nv_Lang->getModule('CTGG'),
    2 => $nv_Lang->getModule('CTYT'),
    3 => $nv_Lang->getModule('CTTT'),
    4 => $nv_Lang->getModule('CTVH'),
    5 => $nv_Lang->getModule('CHO'),
    6 => $nv_Lang->getModule('CTTG'),
    7 => $nv_Lang->getModule('TTT'),
    8 => $nv_Lang->getModule('CTSXSP'),
    9 => $nv_Lang->getModule('CTLKCK'),
    10 => $nv_Lang->getModule('CTKTCB'),
    11 => $nv_Lang->getModule('CTDK'),
    12 => $nv_Lang->getModule('CTNL'),
    13 => $nv_Lang->getModule('CTHC'),
    14 => $nv_Lang->getModule('CTCNN'),
    15 => $nv_Lang->getModule('CTCN'),
    16 => $nv_Lang->getModule('CTTN'),
    17 => $nv_Lang->getModule('CTXLCT'),
    18 => $nv_Lang->getModule('CVCX'),
    19 => $nv_Lang->getModule('NT'),
    20 => $nv_Lang->getModule('NTL'),
    21 => $nv_Lang->getModule('CSHT'),
    22 => $nv_Lang->getModule('NSMT'),
    23 => $nv_Lang->getModule('DCTTH'),
    24 => $nv_Lang->getModule('CTDB'),
    25 => $nv_Lang->getModule('CTDS'),
    26 => $nv_Lang->getModule('CTCAU'),
    27 => $nv_Lang->getModule('CTDTND'),
    28 => $nv_Lang->getModule('CTHH'),
    29 => $nv_Lang->getModule('CTHK'),
    30 => $nv_Lang->getModule('CTTL'),
    31 => $nv_Lang->getModule('CTDD'),
    32 => $nv_Lang->getModule('KHAC'),
];

$array_cancel_reason = [
    "TTQDPL" => $nv_Lang->getModule("reason_ttqdpl"),
    "SPTDT" => $nv_Lang->getModule("reason_sptdt"),
    "PVDT" => $nv_Lang->getModule("reason_pvdt"),
    "CT" => $nv_Lang->getModule("reason_ct"),
    "NTCT" => $nv_Lang->getModule("reason_ntct"),
    "KDUN" => $nv_Lang->getModule("reason_kdun"),
    "TDMT" => $nv_Lang->getModule("reason_tdmt"),
    "HSMT" => $nv_Lang->getModule("reason_hsmt")
];

$array_cancel_reason_cptpp = [
    "TTQDPL" => $nv_Lang->getModule("reason_cptpp_ttqdpl"),
    "SPTDT" => $nv_Lang->getModule("reason_cptpp_sptdt"),
    "KDU" => $nv_Lang->getModule("reason_cptpp_kdu"),
    "PVDT" => $nv_Lang->getModule("reason_cptpp_pvdt")
];

$array_cancel_reason_vk = [
    "KDU" => $nv_Lang->getModule("reason_vk_kdu"),
    "PVDT" => $nv_Lang->getModule("reason_vk_pvdt"), 
    "TTQDPL" => $nv_Lang->getModule("reason_vk_ttqdpl"),
    "SPTDT" => $nv_Lang->getModule("reason_vk_sptdt"),
    "CT" => $nv_Lang->getModule("reason_vk_ct"),
    "NTCT" => $nv_Lang->getModule("reason_vk_ntct"),
    "KDUN" => $nv_Lang->getModule("reason_vk_kdun"),
    "TDMT" => $nv_Lang->getModule("reason_vk_tdmt"), 
    "HSMT" => $nv_Lang->getModule("reason_vk_hsmt"),
    "THK" => $nv_Lang->getModule("reason_vk_thk")
];

//Hàm tính cột tiếp theo của excel theo hướng từ trái sang phải
function getNextColumn($column) {
    $columnNumber = 0;
    $length = strlen($column);
    for ($i = 0; $i < $length; $i++) {
        $columnNumber *= 26;
        $columnNumber += ord($column[$i]) - ord('A') + 1;
    }
    $columnNumber++;
    $columnName = '';
    while ($columnNumber > 0) {
        $modulo = ($columnNumber - 1) % 26;
        $columnName = chr($modulo + ord('A')) . $columnName;
        $columnNumber = intval(($columnNumber - $modulo - 1) / 26);
    }

    return $columnName;
}

// so sánh hai hoặc nhiều mảng đa chiều
function array_diff_assoc_recursive($array1, $array2) {
    $difference = [];

    foreach ($array1 as $key => $value) {
        if (is_array($value)) {
            // Nếu là mảng, so sánh đệ quy
            if (!isset($array2[$key]) || !is_array($array2[$key])) {
                $difference[$key] = $value;
            } else {
                $new_diff = array_diff_assoc_recursive($value, $array2[$key]);
                if (!empty($new_diff)) {
                    $difference[$key] = $new_diff;
                }
            }
        } else {
            // So sánh giá trị đơn giản
            if (!array_key_exists($key, $array2) || $array2[$key] !== $value) {
                $difference[$key] = $value;
            }
        }
    }

    return $difference;
}
function nv_make_file_link(string $file_id, $file_name, $is_new_msc = 1, array $att = [])
{
    // Tạo ra thẻ a chứa link file download bằng agent
    if (empty($file_id)) {
        return '';
    }
    if (!empty($att)) {
        array_walk($att, function(&$a, $b) {$a = $b . '="' . $a . '"';});
    }
    if ($is_new_msc) {
        //return '<a rel="nofollow" href="http://localhost:1234/api/download/file/browser/public?fileId=' . $file_id . '"' . (!empty($att) ? (' ' . implode(' ', $att)) : '') . '>' . $file_name . '</a>';
        return $file_name;
    } else {
        return '<a rel="nofollow" href="https://muasamcong.mpi.gov.vn/edoc-oldproxy-service/api/download/file/browser?filePath=' . urlencode('/WAS/' . $file_id) . '"' . (!empty($att) ? (' ' . implode(' ', $att)) : '') . '>' . $file_name . '</a>';
    }
}

// Tính toán số tiền được khấu trừ khi nâng cấp từ vieweb => pro1 => vip1
function cal_upgrade_dededuction($vip) {
    $money_per_day = floor($vip['deal_price'] / ($vip['time_month'] / 12 * 365));
    $end = new DateTime('@'.$vip['end_time']);
    $current = new DateTime('@'.NV_CURRENTTIME);
    $remain_days = $end->diff($current)->days;
    
    return floor($remain_days * $money_per_day);
}

if (NV_LANG_DATA == 'en') {
    $dtnet_module_bids = 'bids';
    $dtnet_module_bids_tbmt = 'bidding';
    $dtnet_module_bids_plan = 'plans';
    $dtnet_module_bids_result = 'result';
    $dtnet_module_profile = 'bids-profile';
} else {
    $dtnet_module_bids = 'dau-thau';
    $dtnet_module_bids_tbmt = 'tbmt';
    $dtnet_module_bids_plan = 'ke-hoach';
    $dtnet_module_bids_result = 'result';
    $dtnet_module_profile = 'dn';
}

function fetchProfileData($profile_ids) {
    global $module_config, $module_name, $db_config, $dtnet_module_profile;
    if (empty($profile_ids)) {
        return [];
    }

    $nukeVietElasticSearch = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_result_host'], $module_config[$module_name]['elas_result_port'], 'bids_profile', $module_config[$module_name]['elas_result_user'], $module_config[$module_name]['elas_result_pass']);

    $search_esdtnet = [
        'must' => [
            [
                'terms' => [
                    'id' => array_keys($profile_ids)
                ]
            ]
        ]
    ];

    $array_query_elastic_dtnet = [
        'query' => [
            'bool' => $search_esdtnet
        ]
    ];

    $response = $nukeVietElasticSearch->search_data($db_config['prefix'] . '_bids_profile', $array_query_elastic_dtnet);
    $arr_profile = [];
    foreach ($response['hits']['hits'] as $v) {
        if (!empty($v['_source'])) {
            $view = $v['_source'];
            $view['link_invector'] = URL_DTNET_SITE . NV_LANG_DATA . '/' . $dtnet_module_profile . '/' . $view['prof_alias'] . '/';
            $arr_profile[$view['id']] = $view;
        }
    }

    return $arr_profile;
}

function convertTimeUnit($input) {
    global $nv_Lang;

    $input = preg_replace('/\bD\b/', $nv_Lang->getModule('date_ehsdt'), $input);
    $input = preg_replace('/\bM\b/', $nv_Lang->getModule('type_pay_time1'), $input);
    $input = preg_replace('/\bY\b/', $nv_Lang->getModule('type_pay_time2'), $input);

    return trim($input);
}
