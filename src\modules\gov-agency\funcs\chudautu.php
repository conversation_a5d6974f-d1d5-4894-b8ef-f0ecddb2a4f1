<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2022 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Wed, 11 May 2022 09:11:42 GMT
 */
if (!defined('NV_IS_MOD_GOV_AGC')) {
    die('Stop!!!');
}

$description = $nv_Lang->getModule('chudautu_func_desc');
$key_words = $module_info['keywords'];

$msc_agc_id = 0;

$alias = explode('-', $array_op[0]);
$msc_agc_id = intval($alias[sizeof($alias) - 1]);
if ($msc_agc_id < 0) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
}

$array_data = [];
$page = $nv_Request->get_page('page', 'get,post', 1);
$per_page = 20;

$round = $nv_Request->get_int('round', 'post,get', 0);
$source = $nv_Request->get_int('source', 'post,get', 0);

// lấy từ cookie trước
$t_range = intval($nv_Request->get_string('tc_range', 'cookie', date('Y', NV_CURRENTTIME)));
$st_range = $nv_Request->get_int('t_range', 'get,post', $t_range);
if ($t_range != $st_range) {
    $nv_Request->set_Cookie('tc_range', $st_range);
}

$agency = $agency_list[$msc_agc_id];

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $array_op[0] . '/' . $array_op[1];
$where = [];

$where[]= 'id_agency = ' . $msc_agc_id;

if ($st_range == 3 || $st_range == 6) {
    list ($d, $m, $y) = explode('/', date('j/n/Y', NV_CURRENTTIME));
    $time1 = strtotime('-'.$st_range.' months', mktime(0, 0, 0, intval($m), intval($d), intval($y)));
    $where[] = 'date_post <= ' . NV_CURRENTTIME;
    $where[] = 'date_post >= ' . $time1;
    
} else {
    $time1 = mktime(0, 0, 0, 1, 1, $st_range);
    $time2 = mktime(23, 59, 59, 12, 31, $st_range);
    $where[] = 'date_post <= ' . $time2;
    $where[] = 'date_post >= ' . $time1;
}

$base_url .= '&amp;t_range=' . $st_range;

if (!empty($source)) {
    $where[] = 'id_classify = ' . $source;
    $base_url .= '&amp;source=' . $source;
}

!empty($id_agency) && $where[] = 'id_agency=' . $db->quote($id_agency);

$per_page = 20;
$page = $nv_Request->get_page('page', 'post,get', 1);

$sort = $nv_Request->get_int('sort', 'post,get', 1);
$link_sort = [
    'num' => $base_url . '&amp;sort=' . 1,
    'numbid' => $base_url . '&amp;sort=' . 3,
    'invest' => $base_url . '&amp;sort=' . 5,
    'bid' => $base_url . '&amp;sort=' . 7
];
if ($sort > 0 && $sort < 9) {
    $base_url .= '&amp;sort=' . $sort;
} else {
    $sort = 0;
}

$round > 2 && $round = 0;
if ($round > 0) {
    $base_url .= '&amp;round=' . $round;
}

$page_url = $base_url;
$page > 1 && $page_url .= '&amp;page=' . $page;

$db->sqlreset()->select('count(DISTINCT id_soclitor) as c, COUNT(*) as p, SUM(total_investment) AS i, SUM(total_bidding) AS b, SUM(total_bid) AS d')
    ->from('nv4_vi_gov_agency_static')
    ->where(implode(' AND ', $where));

$num = $db->query($db->sql())
    ->fetch();
$num_items = $num['c'];
$sum = [
    'total_num_projects' => nv_number_format($num['p']),
    'total_num_bid' => $num['d'] ? nv_number_format($num['d']) : 0,
    'total_invest' => $num['i'] ? round_number_format($num['i'], $round) : 0,
    'total_price_bid' => $num['b'] ? round_number_format($num['b'], $round) : 0,
];
$db->select('id_soclitor, id_agency, management_c_code, solictor,
        COUNT(id_project) AS num_projects,
        SUM(total_bid) AS num_bid,
        SUM(total_investment) AS total_invest,
        SUM(total_bidding) AS total_price_bid')
    ->from('nv4_vi_gov_agency_static')
    ->where(implode(' AND ', $where))
    ->group('id_soclitor')
    ->order('num_projects DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
if ($sort > 0) {
    switch ($sort) {
        case 1:
            $db->order('num_projects DESC');
            $link_sort['num'] = $base_url . '&amp;sort=' . 2;
            break;
        case 2:
            $db->order('num_projects ASC');
            $link_sort['num'] = $base_url . '&amp;sort=' . 1;
            break;
        case 3:
            $db->order('num_bid DESC');
            $link_sort['numbid'] = $base_url . '&amp;sort=' . 4;
            break;
        case 4:
            $db->order('num_bid ASC');
            $link_sort['numbid'] = $base_url . '&amp;sort=' . 3;
            break;
        case 5:
            $db->order('total_invest DESC');
            $link_sort['invest'] = $base_url . '&amp;sort=' . 6;
            break;
        case 6:
            $db->order('total_invest ASC');
            $link_sort['invest'] = $base_url . '&amp;sort=' . 5;
            break;
        case 7:
            $db->order('total_price_bid DESC');
            $link_sort['bid'] = $base_url . '&amp;sort=' . 8;
            break;
        case 8:
            $db->order('total_price_bid ASC');
            $link_sort['bid'] = $base_url . '&amp;sort=' . 7;
    }
}

$sth = $db->prepare($db->sql());
$sth->execute();

while ($view = $sth->fetch()) {
    $code_size = 0;
    $src_management = [];
    $view['management_c_code'] && $code_size = sizeof(explode('.', $view['management_c_code']));
    switch ($code_size) {
        case 1:
            $src_management = $c1_list;
            break;
            
        case 2:
            $src_management = $c2_list;
            break;
            
        case 3:
            $src_management = $c3_list;
            break;
            
        case 4:
            $src_management = $c4_list;
            break;
    }
    
    $view['id_agency'] = isset($agency_list[$view['id_agency']]) ? $agency_list[$view['id_agency']]['title'] : '';
    $view['management_c_code'] = isset($src_management[$view['management_c_code']]) ? $src_management[$view['management_c_code']]['title'] : 'N/A';
    $view['total_invest'] = round_number_format($view['total_invest'], $round);
    $view['total_price_bid'] = round_number_format($view['total_price_bid'], $round);
    
    $view['link_solicitor'] = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=bidding&amp;" . NV_OP_VARIABLE . "=procuring-entity/" . strtolower(change_alias($view['solictor'])) . '-' . $view['id_soclitor']);
    $array_data[] = $view;
}

$generate_page = '';
if ($num_items > $per_page) {
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
}
$page_title = $nv_Lang->getModule('cdt_page_title', $agency['title']);
$canonicalUrl = getCanonicalUrl($page_url);
betweenURLs($page, ceil($num_items / $per_page), $base_url, '&page=', $prevPage, $nextPage);
$array_mod_title[] = [
    'title' => $agency['title'],
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $array_op[0]
];

$form_action = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $array_op[0] . '/' . $array_op[1];
$array_mod_title[] = [
    'title' => $nv_Lang->getModule('solictor'),
    'link' => $form_action
];

$contents = nv_theme_bidding_solicitor($agency, $array_data, $generate_page, $page, $per_page, $st_range, $sort, $link_sort, $sum, $round, $num_items, $form_action);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
