var formObject = $("[id*=bltablesearch]");
var price_active = [];

function bl_advanceShow() {
    $(".advance-search", formObject).is(":hidden") && $(".advance-search", formObject).slideDown();
    $('input[name="is_advance"]', formObject).val(1);
    $(".btn-search em", formObject).removeClass($(".btn-search", formObject).data("icon-search-simple")).addClass($(".btn-search", formObject).data("icon-search-advance"));
    $(".btn-search .txt", formObject).text($(".btn-search", formObject).data("search-simple"))
}

function bl_advanceHide() {
    $(".advance-search", formObject).not(":hidden") && $(".advance-search", formObject).slideUp();
    $('input[name="is_advance"]', formObject).val(0);
    $(".btn-search em", formObject).removeClass($(".btn-search", formObject).data("icon-search-advance")).addClass($(".btn-search", formObject).data("icon-search-simple"));
    $(".btn-search .txt", formObject).text($(".btn-search", formObject).data("search-advance"))
    reset_validate();
}

function bl_advanceBtnShow() {
    var a = $(".btn-search", formObject);
    a.is(":hidden") && a.show();
    a.parents(".panel").is(":hidden") && a.parents(".panel").show()
}

function bl_advanceBtnHide() {
    var a = $(".btn-search", formObject);
    a.parents(".panel").not(":hidden") && a.parents(".panel").hide();
    a.not(":hidden") && a.hide()
}

function bl_typeContentShow() {
    $("[name=search_type_content]", formObject).parents(".form-group").is(":hidden") && $("[name=search_type_content]", formObject).parents(".form-group").show()
    $("[name=searchkind]", formObject).parents(".form-group").is(":hidden") && $("[name=searchkind]", formObject).parents(".form-group").show()
}

function bl_typeContentHide() {
    $("[name=search_type_content]", formObject).parents(".form-group").not(":hidden") && $("[name=search_type_content]", formObject).parents(".form-group").hide()
    $("[name=searchkind]", formObject).parents(".form-group").not(":hidden") && $("[name=searchkind]", formObject).parents(".form-group").hide()
}

function bl_checkSearchForm(data) {
    var type = parseInt($("[name=type_info]", formObject).val()), type2 = parseInt($("[name=type_info2]", formObject).val()), type3 = parseInt($("[name=type_info3]", formObject).val()), adv = parseInt($("[name=is_advance]").val()), is_error = false, error_mess = '', error_input = false;
    var is_province = parseInt($('[name=is_province]', formObject).val());
    const type_info = getIntFormValue('type_info');
    $(".has-error", formObject).removeClass("has-error");

    $("input[name][type=text]:not(.money-format)", formObject).each(function() {
        var v = trim($(this).val());
        v = v.replace(/\s+/g, ' ');
        if (v != '') {
            v = trim(strip_tags(v));
        }
        $(this).val(v);
        v2 = v.replace(/\s+/g, '');
        if (v != '' && v2.length < 2) {
            if (!is_error) {
                is_error = true;
                error_mess = $(this).attr("data-error");
                error_input = this;
            }
        }
    });

    if (is_error) {
        modalShow('', error_mess, () => {}, () => {
            $(error_input).parent().addClass("has-error");
            $(error_input).focus()
        });
        return false
    }

    if (parseInt($("[name=type_search]:checked", formObject).val()) == 2) {
        $("[name=type_info], [name=search_type_content], [name=seachkind]", formObject).removeAttr("name");
        if (type2 == 2) {
            $(".search_khlcnt [name], .search_result_block [name]", formObject).removeAttr("name");
        } else if (type2 == 4) {
            $(".search_result_block [name], .search_tbmt [name], .price_contract [name]", formObject).removeAttr("name");
        } else if (type2 == 5) {
            $(".search_khlcnt [name], .search_tbmt [name]", formObject).removeAttr("name");
        }
    } else {
        $("[name=type_info2]", formObject).removeAttr("name");
        if (type == 1) {
            $(".search_khlcnt [name], .search_result_block [name]", formObject).removeAttr("name");
        } else if (type == 2) {
            $(".search_tbmt [name], .search_result_block [name]", formObject).removeAttr("name");
        } else if (type == 3) {
            $(".search_khlcnt [name], .search_tbmt [name], .htluachontb_filter [name]", formObject).removeAttr("name");
        }
    }

    $("input[name]", formObject).each(function() {
        if ($(this).val() == '') {
            $(this).removeAttr("name")
        }
    });

    $("input[name].money", formObject).each(function() {
        if (parseInt($(this).val()) <= 0) {
            $(this).removeAttr("name")
        }
    });

    sessionStorage.setItem('biddingsearchstate', 'submited');

    // Kiểm tra điều kiện redirect đến URL tỉnh thành
    if (type_info === 1 && parseInt($("[name=type_search]:checked", formObject).val()) === 1) {
        const selectedAliases = getSelectedAliases();
        const q = getFormValue('q');
        const q2 = getFormValue('q2');
        const without_key = getFormValue('without_key');
        const selectedFields = getSelectedFields();
        const selectedPhuongthuc = getSelectedPhuongthuc();
        const searchkind_all = getSelectedRadio('searchkind');
        const goods = getSelectedRadio('goods');
        const type_info3 = getIntFormValue('type_info3');
        const type_choose_id = getIntFormValue('type_choose_id');
        const goods_2 = getIntFormValue('goods_2');
        const type_view_open = getIntFormValue('type_view_open');
        const sl_nhathau = getIntFormValue('sl_nhathau');
        const idregion = getIntFormValue('idregion');
        const type_org = getSelectedRadio('type_org');
        const keyword_id_bidder = getIntFormValue('keyword_id_bidder');
        const keyword_id_province = getIntFormValue('keyword_id_province');
        const keyword_id_district = getIntFormValue('keyword_id_district');
        const khlcnt = getIntFormValue('khlcnt');
        const dateRangeChanged = updateDateRangeInputs();

        // Kiểm tra điều kiện để redirect đến URL SEO tỉnh thành
        const shouldRedirect = (
            selectedAliases.length === 1 &&
            selectedAliases[0] !== '' &&
            q === '' && q2 === '' && without_key === '' &&
            selectedFields.length === 0 && selectedPhuongthuc.length === 0 &&
            type_info3 === 1 && type_choose_id === 0 && goods_2 === 0 &&
            type_view_open === 0 && sl_nhathau === 0 && idregion === 0 &&
            type_org === '1' && goods === '0' &&
            keyword_id_bidder === 0 && keyword_id_province === 0 &&
            keyword_id_district === 0 && khlcnt === 0 &&
            searchkind_all === '0' && !dateRangeChanged
        );

        if (shouldRedirect) {
            const provinceAlias = selectedAliases[0];
            const defaultSFrom = $('input[name="sfrom"]').data('default');
            const defaultSTo = $('input[name="sto"]').data('default');
            const actionUrl = formObject.data("action16") + encodeURIComponent(provinceAlias) +
                "?sfrom=" + encodeURIComponent(defaultSFrom) + "&sto=" + encodeURIComponent(defaultSTo) + "&is_advance=1";
            window.location.href = actionUrl;
            return false;
        }
    }

    if (is_province === 1 && type_info === 1) {
        return false;
    } else {
        return true;
    }
}

function bl_formReset() {
    $(".has-error", formObject).removeClass("has-error");
    $("[data-default]", formObject).each(function() {
        if ($(this).is("input[type=text], input[type=hidden]")) {
            $(this).val($(this).attr("data-default"))
        } else if ($(this).is("select")) {
            $(this).val($(this).attr("data-default"));
            $('#keyword_id_bidder').val(0).trigger('change.select2');
            $('#keyword_id_province').val(0).trigger('change.select2');
            $('#keyword_id_district').val(0).trigger('change.select2');
            $('#keyword_id_district').prop('disabled', true);
            if ($('#rq_province').length) {
                $('#rq_province').val(-1).trigger('change.select2');
                $('#rq_district').val(0).trigger('change.select2');
                $('#rq_ward').val(0).trigger('change.select2');
                $('#district_container').hide();
                $('#ward_container').hide();
            }
            $('.selected_phanmuc').val(null).trigger('change.select2');
            $('#idprovince').val(null).trigger('change.select2');
            $('#idprovince_kh').val(null).trigger('change.select2');
            $('#idprovincekq').val(null).trigger('change.select2');
            $('#phanmucid').val(null).trigger('change.select2');
        } else if ($(this).is("input[type=radio]")) {
            if ($(this).attr("data-default") == -1) {
                $(this).prop('checked', false);
            } else {
                $(this).filter("[value=" + $(this).attr("data-default") + "]").prop('checked', true);
            }
        } else if ($(this).is("input[type=checkbox]")) {
            var regex = /^\s*(true|1|on)\s*$/i;
            $(this).prop('checked', regex.test($(this).attr("data-default")))
        }
    });
    //Không đóng form nâng cao khi reset form
    if (parseInt($('input[name="is_advance"]').val()) !== 1) {
        $(".btn-search", formObject).trigger('click');
    }
    $('input[name="field[]"]').trigger('change');
    bl_setDaterangepicker({ 'startDate' : $("[name=sfrom]", formObject).attr("data-default"), 'endDate' : $("[name=sto]", formObject).attr("data-default") });
    bl_changeTypeSearch();
    $('html, body').animate({ scrollTop : formObject.offset().top }, 800);
}

function bl_setDaterangepicker(_options) {
    var maxspan = parseInt(formObject.attr("data-maxspan")), mindate = formObject.attr("data-mindate"), opens = formObject.attr("data-opens"), _maxspanDate = moment().subtract(maxspan, 'months');

    // Menu khoảng tìm kiếm
    var ranges = {};
    ranges[$(".search_range", formObject).attr("data-today-lang")] = [ moment(), moment() ];
    // ranges[$(".search_range", formObject).attr("data-lastday-lang")] =
    // [moment().subtract(1, 'days'), moment()];
    ranges[$(".search_range", formObject).attr("data-last7days-lang")] = [ moment().subtract(6, 'days'), moment() ];
    ranges[$(".search_range", formObject).attr("data-last14days-lang")] = [ moment().subtract(13, 'days'), moment() ];
    ranges[$(".search_range", formObject).attr("data-last30days-lang")] = [ moment().subtract(29, 'days'), moment() ],
        // ranges[$(".search_range", formObject).attr("data-thismonth-lang")] =
        // [moment().startOf('month'), moment().endOf('month')];
        // ranges[$(".search_range", formObject).attr("data-lastmonth-lang")] =
        // [moment().subtract(1, 'month').startOf('month'), moment().subtract(1,
        // 'month').endOf('month')];
        ranges[$(".search_range", formObject).attr("data-last3months-lang")] = [ moment().subtract(3, 'months'), moment() ];
    ranges[$(".search_range", formObject).attr("data-last6months-lang")] = [ moment().subtract(6, 'months'), moment() ];
    ranges[$(".search_range", formObject).attr("data-last-maxspan-lang")] = [ _maxspanDate, moment() ];

    var allday = $(".search_range", formObject).data('lastall');
    if (allday != '') {
        ranges[allday] = [ moment(mindate, "DD/MM/YYYY"), moment() ];
    }

    var calendar_options = {
        showDropdowns: true,
        locale: {
            cancelLabel: $(".search_range", formObject).attr("data-cancel-lang"),
            applyLabel: $(".search_range", formObject).attr("data-apply-lang"),
            customRangeLabel: $(".search_range", formObject).attr("data-customrange-lang"),
            format: "DD/MM/YYYY",
        },
        ranges: ranges,
        startDate: moment().subtract(6, 'days'),
        endDate: moment(),
        minDate: moment(mindate, "DD/MM/YYYY"),
        maxDate: moment(),
        opens: opens,
        drops: "auto",
        linkedCalendars: false
    };

    $.extend(calendar_options, _options);

    $(".search_range", formObject).daterangepicker(calendar_options, function(start, end, label) {
        $("[name=sfrom]", formObject).val(start.format('DD/MM/YYYY'));
        $("[name=sto]", formObject).val(end.format('DD/MM/YYYY'))
    });
}

function bl_changeTypeSearch() {
    reset_validate();
    var a = parseInt($("[name=type_search]:checked", formObject).val());
    var par_search = $(".par_search", formObject).data("par-search");
    var par_search2 = $(".par_search", formObject).data("par-search2");
    if (a == 2) {
        $("[name=type_info2]", formObject).show();
        $("[name=type_info]", formObject).hide();
        $("[name=type_info3]", formObject).hide();
        $(".par_search .txt", formObject).text(par_search);
        bl_changeTypeInfo2()
    } else if (a == 3) {
        $("[name=type_info3]", formObject).show();
        $("[name=type_info2]", formObject).hide();
        $("[name=type_info]", formObject).hide();
        $(".par_search .txt", formObject).text(par_search2);
        bl_changeTypeInfo3()
    } else {
        $("[name=type_info3]", formObject).hide();
        $("[name=type_info]", formObject).show();
        $("[name=type_info2]", formObject).hide();
        $(".par_search .txt", formObject).text(par_search);
        bl_changeTypeInfo()
    }
    bl_removField()
}

function bl_changeTypeInfo3() {
    var type3 = parseInt($("[name=type_info3]", formObject).val()), is_advance = parseInt($('input[name="is_advance"]').val());
    $('#main_keynote').attr('data-original-title', daugiaKeywordNote[type3]);
    $(".search_khlcnt, .search_result_block, .search_tbmt, .search_bidfieid", formObject).hide();
    $(".search_tbmt", formObject).hide();
    $(".search_dau_gia", formObject).show();
    $(".search_devproject", formObject).hide();
    $(".htluachon_filter", formObject).hide();
    $(".htluachontb_filter", formObject).hide();
    $(".search_phanmuc", formObject).hide();
    $(".search_kqmt", formObject).hide();
    $(".search_kqcgtt", formObject).hide();
    $(".search_ycbg", formObject).hide();
    $(".search_hddt", formObject).hide();
    if (type3 == 1) {
        formObject.attr("action", formObject.data("action7"));
        $('#gr_location').css("display", "block");
        $('#gr_price').css("display", "block");
        $('#gr_organization').css("display", "block");
        $('select[name="ketqua_luachon_tochuc_dgts"]').css("display", "none");
    } else {
        formObject.attr("action", formObject.data("action8"));
        $('#gr_location').css("display", "block");
        $('#gr_price').css("display", "block");
        $('#gr_organization').css("display", "none");
        $('select[name="ketqua_luachon_tochuc_dgts"]').css("display", "block");
    }

    bl_advanceBtnShow();
    if (is_advance == 1) {
        bl_advanceShow();
    } else {
        bl_advanceHide();
    }
    bl_typeContentShow();
}
function bl_changeTypeInfo2() {
    var type2 = parseInt($("[name=type_info2]", formObject).val()), is_advance = parseInt($('input[name="is_advance"]').val());

    if (ndtKeywordNote[type2]) {
        $('#main_keynote').attr('data-original-title', ndtKeywordNote[type2]);
    } else {
        $('#main_keynote').attr('data-original-title', ndtKeywordNote[0]);
    }

    bl_typeContentHide();
    $('select[name="ketqua_luachon_tochuc_dgts"]').css("display", "none");
    $(".search_khlcnt, .search_result_block, .search_tbmt, .search_bidfieid", formObject).hide();
    $(".search_dau_gia", formObject).hide();
    $(".search_devproject", formObject).hide();
    $('.province_sel').hide();
    $('.fg_selRegions_kh').hide();
    $('.fg_selProvinces_kh').hide();
    $(".htluachon_filter", formObject).hide();
    $(".htluachontb_filter", formObject).hide();
    $(".search_phanmuc", formObject).hide();
    $(".search_kqmt", formObject).hide();
    $(".search_kqcgtt", formObject).hide();
    $(".search_ycbg", formObject).hide();
    $(".search_hddt", formObject).hide();
    if (type2 == 2) {
        $(".search_tbmt", formObject).show();
        $(".ls_cat", formObject).hide();
        formObject.attr("action", formObject.data("action"));
    } else if (type2 == 4) {
        $(".search_khlcnt", formObject).show();
        formObject.attr("action", formObject.data("action1"));
    } else if (type2 == 5) {
        $(".search_result_block", formObject).hide();
        formObject.attr("action", formObject.data("action2"));
    } else if (type2 == 3) {
        formObject.attr("action", formObject.data("action3"));
    } else if (type2 == 6) {
        formObject.attr("action", formObject.data("action4"));
    } else if (type2 == 8) {
        formObject.attr("action", formObject.data("action21"));
    } else {
        formObject.attr("action", formObject.data("action6"));
    }

    // Chỉ có loại `Thông báo khảo sát sự quan tâm` là ko có tìm kiếm nâng cao
    if ([1, 3, 6, 8].indexOf(type2) > -1) {
        bl_advanceHide();
        bl_advanceBtnHide();
    } else {
        bl_advanceBtnShow();
        if (is_advance == 1) {
            bl_advanceShow();
        } else {
            bl_advanceHide();
        }
    }
}

function bl_changeLocationRegion() {
    let provinceIds = $('.selRegions').find(":selected").attr('data-province-ids');
    if (provinceIds) {
        let provinceArray = "[" + provinceIds + "]";
        provinceArray = JSON.parse(provinceArray);
        $('.selProvinces').val(provinceArray).trigger('change');
    } else {
        $('.selProvinces').val([]).trigger('change');
    }
}

function bl_changeLocationRegionPlan() {
    let provinceIds = $('.selRegions_kh').find(":selected").attr('data-province-ids');
    if (provinceIds) {
        let provinceArray = "[" + provinceIds + "]";
        provinceArray = JSON.parse(provinceArray);
        $('.selProvinces_kh').val(provinceArray).trigger('change');
    } else {
        $('.selProvinces_kh').val([]).trigger('change');
    }
}

function bl_changeTypeInfo() {
    reset_validate();
    var type = parseInt($("[name=type_info]", formObject).val());
    if (keywordNote[type]) {
        $('#main_keynote').attr('data-original-title', keywordNote[type]);
    } else {
        $('#main_keynote').attr('data-original-title', keywordNote[0]);
    }

    $('select[name="ketqua_luachon_tochuc_dgts"]').css("display", "none");
    $(".search_khlcnt, .search_tbmt, .search_result_block, .search_kqmt", formObject).hide();
    $(".search_kqcgtt", formObject).hide();
    $(".search_dau_gia", formObject).hide();
    $(".search_devproject", formObject).hide();
    $(".search_phanmuc", formObject).hide();
    $(".htluachon_filter", formObject).hide();
    $('.province_sel', formObject).show();
    $(".provinceid_kqlcnt", formObject).hide();
    $(".provinceid_khttlcnt", formObject).hide();
    $(".goods_kqlcnt", formObject).hide();
    $(".search_ycbg", formObject).hide();
    $(".search_hddt", formObject).hide();
    $(".rq_form_value", formObject).hide();
    $(".htluachontb_filter", formObject).hide();
    $(".search_type_kqlcnt", formObject).hide();
    $(".search_bidfieid", formObject).hide();

    if (type == 1) {
        $(".search_tbmt", formObject).show();
        $(".search_phanmuc", formObject).show();
        $(".htluachontb_filter", formObject).show();
        formObject.attr("action", formObject.data("action11"))
    } else if (type == 2) {
        $('.fg_selRegions_kh').show();
        $('.fg_selProvinces_kh').show();
        $(".search_khlcnt", formObject).show();
        formObject.attr("action", formObject.data("action1"))
    } else if (type == 3) {
        $(".search_result_block").show();
        $(".htluachon_filter").show();
        $(".search_type_kqlcnt", formObject).show();
        $(".search_bidfieid", formObject).show();
        $(".provinceid_kqlcnt", formObject).show();
        $(".goods_kqlcnt", formObject).show();
        formObject.attr("action", formObject.data("action2"))
    } else if (type == 4 || type == 12) {
        formObject.attr("action", formObject.data("action3"));
    } else if (type == 5) {
        formObject.attr("action", formObject.data("action5"));
    } else if (type == 6 || type == 13) {
        formObject.attr("action", formObject.data("action4"));
    } else if (type == 10 || type == 14) {
        formObject.attr("action", formObject.data("action10"));
    } else if (type == 15) {
        formObject.attr("action", formObject.data("action15"));
        $(".search_devproject", formObject).show();
        $(".khlcnt_type", formObject).hide();
        $(".provinceid_khttlcnt", formObject).show();
    } else if (type == 7) {
        $(".search_devproject", formObject).show();
        $(".khlcnt_type", formObject).show();
        formObject.attr("action", formObject.data("action9"))
    } else if (type == 17) {
        $(".search_ycbg", formObject).show();
        $(".rq_form_value", formObject).show();
        $("[name=search_type_content]", formObject).parents(".form-group").hide();
        formObject.attr("action", formObject.data("action17"))
    } else if (type == 22) {
        $(".search_hddt", formObject).show();
        $("[name=search_type_content]", formObject).parents(".form-group").hide();
        formObject.attr("action", formObject.data("action22"))
    } else if (type == 42) {
        $(".search_kqcgtt", formObject).show();
        formObject.attr("action", formObject.data("action42"))
    } else {
        $(".search_tbmt", formObject).show();
        $(".search_phanmuc", formObject).show();
        bl_setAliasAction();
    }
    if (type == 5) {
        $(".search_kqmt", formObject).show();
    }
    if (type == 4 ||type == 12 || type == 6 || type == 10 || type == 13 || type == 14) {
        bl_advanceHide();
        bl_advanceBtnHide();
        bl_typeContentHide();
    } else {
        bl_advanceBtnShow();
        if (parseInt($('[name="is_advance"]', formObject).val()) == 0) {
            bl_advanceHide();
        } else {
            bl_advanceShow();
        }
        if (type == 15) {
            bl_typeContentHide();
        } else {
            bl_typeContentShow();
        }
        if (type == 17) {
            $("[name=search_type_content]", formObject).parents(".form-group").hide();
        }
    }
}

function bl_setAliasAction() {
    type_search = parseInt($('[name=type_search]:checked', formObject).val());
    type_info = parseInt($('[name=type_info]', formObject).val());
    is_advance = $('input[name="is_advance"]', formObject).val();
    is_province = $('input[name="province"]', formObject).val();
    type_ht_tbmt_action = $('[name="cat"]:checked', formObject).data('action');
    type_org_action = $('[name="type_org"]:checked', formObject).data('action');
    var selectedAliases = $('#idprovince option:selected').map(function() {
        return $(this).data('alias');
    }).get();
    if (is_advance == 1 && type_search == 1 && type_info == 1) {
        if (type_ht_tbmt_action != undefined && type_ht_tbmt_action != '') {
            formObject.attr("action", type_ht_tbmt_action);
        } else if (type_org_action != undefined && type_org_action != '') {
            formObject.attr("action", type_org_action);
        } else {
            $('#idprovince').on('change', updateFormAction);
            $('[name=q], [name=q2], [name=without_key], [name=idregion]', formObject).on('input', updateFormAction);
            $('input[name="field[]"], input[name="phuong_thuc[]"], input[name="bidfieid[]"], input[name=type_org], input[name="searchkind"], input[name="goods"]').on('change', updateFormAction);
            $('.search_range').on('change', updateFormAction);

        }
    } else {
        formObject.attr("action", formObject.data("action11"));
    }
}

function bl_removField() {
    if (parseInt($("[name=type_search]:checked", formObject).val()) == 2 || parseInt($("[name=type_search]:checked", formObject).val()) == 3) {
        $("[id='ccb_field_block_1'], [id='ccb_field_block_2'], [id='ccb_field_block_3'], [id='ccb_field_block_4'], [id='ccb_field_block_5']", formObject).parent().hide();
        $("[id='ccb_field_block_7'], [id='ccb_field_block_8'], [id='ccb_field_block_9'], [id='ccb_field_block_10']", formObject).parent().show();
        $(".type_org", formObject).hide();
        $('.province_sel').hide();
        $(".ls_cat", formObject).hide();
        $(".price_contract", formObject).hide()
    } else {
        $("[id='ccb_field_block_1'], [id='ccb_field_block_2'], [id='ccb_field_block_3'], [id='ccb_field_block_4'], [id='ccb_field_block_5']", formObject).parent().show();
        $("[id='ccb_field_block_7'], [id='ccb_field_block_8'], [id='ccb_field_block_9'], [id='ccb_field_block_10']", formObject).parent().hide();
        $(".type_org", formObject).show();
        $('.province_sel', formObject).show();
        // $(".ls_cat", formObject).show();
    }

};

$(function() {
    $('.fselect2').select2({ language : nv_lang_interface });
    bl_setDaterangepicker({ startDate : $("[name=sfrom]", formObject).val(), endDate : $("[name=sto]", formObject).val() });
    $("[name=type_search]", formObject).on("change", function(a) {
        bl_changeTypeSearch();
        $(".ccb_field", formObject).prop("checked", !1)
    });
    $(".money-format", formObject).on("keyup change", function() {
        let money = str2num($(this).val());
        if(money > 9223372036854775000) {
            money = 9223372036854775000;
            $("input[data-name=" + $(this).attr("data-name") + "]").tooltip({'title': 'Vui lòng nhập khoảng giá hợp lệ (giá phải nhỏ hơn hoặc bằng 9,223,372,036,854,775,000 VNĐ)'}).tooltip("show");
        }
        let money_format = String(money);
        money = FormatMoney(money_format);
        $(this).val(money);
        $("[name=" + $(this).attr("data-name") + "]", formObject).val(str2num(money));
    });
    $(".btn-search", formObject).click(function(a) {
        a.preventDefault();
        type_info = parseInt($("[name=type_info]", formObject).val());
        type_search = parseInt($("[name=type_search]:checked", formObject).val());
        parseInt($('input[name="is_advance"]', formObject).val()) ? bl_advanceHide() : bl_advanceShow()
        if (![2, 3].includes(type_search) && ![2, 3, 4, 5, 6, 7, 10, 15, 17, 42].includes(type_info)) {
            bl_setAliasAction();
        }
    });

    // Nếu chọn tìm theo tên hoặc mã thì không tìm theo hàng hóa và ngược lại
    $("[name=par_search]", formObject).click(function(a) {
        if ($(this).is(':checked') && $('[name=goods]:checked', formObject).val() > 0) {
            $("[name=goods][value=0]", formObject).prop("checked", true);
        }
        if ($(this).is(':checked') && $('[name=rq_form_value]:checked').val() > 0) {
            $("[name=rq_form_value][value=0]").prop("checked", true);
        }
    });
    $("[name=goods]", formObject).click(function(a) {
        if ($(this).val() > 0 && $('[name=par_search]', formObject).is(':checked')) {
            $("[name=par_search]", formObject).prop("checked", false);
        }
    });
    $("[name=rq_form_value]").click(function () {
        console.log('vao day');
        if ($(this).val() > 0 && $('[name=par_search]').is(':checked')) {
            $("[name=par_search]").prop("checked", false);
        }
    });

    // bl_changeTypeSearch();

    $("#money_from, #money_to", formObject).on("keyup change focusout", function() {
        validate_money("money_from", "money_to");
    });

    $("#win_price_from, #win_price_to", formObject).on("keyup change focusout", function() {
        validate_money("win_price_from", "win_price_to");
    });

    $("#price_from, #price_to", formObject).on("keyup change focusout", function() {
        validate_money("price_from", "price_to");
    });

    $("#tbmt_price_from, #tbmt_price_to", formObject).on("keyup change focusout", function() {
        validate_money("tbmt_price_from", "tbmt_price_to");
    });

    $("#invest_from, #invest_to", formObject).on("keyup change focusout", function() {
        validate_money("invest_from", "invest_to");
    });

    $("#dev_invest_from, #dev_invest_to", formObject).on("keyup change focusout", function() {
        validate_money("dev_invest_from", "dev_invest_to");
    });

    $("#price_plan_from, #price_plan_to", formObject).on("keyup change focusout", function() {
        validate_money("price_plan_from", "price_plan_to");
    });

    $("#result_price_plan_from, #result_price_plan_to", formObject).on("keyup change focusout", function() {
        validate_money("result_price_plan_from", "result_price_plan_to");
    });

    $("#kqmt_price_plan_from, #kqmt_price_plan_to", formObject).on("keyup change focusout", function() {
        validate_money("kqmt_price_plan_from", "kqmt_price_plan_to");
    });

    $("#contract_price_plan_from, #contract_price_plan_to", formObject).on("keyup change focusout", function() {
        validate_money("contract_price_plan_from", "contract_price_plan_to");
    });

    $("#keyword_min_bid_prices, #keyword_max_bid_prices", formObject).on("keyup change focusout", function() {
        validate_money("keyword_min_bid_prices", "keyword_max_bid_prices");
    });

    bl_removField();
    /*
     * var timerResetPicker = 0; $(window).on('resize', function() { if
     * (timerResetPicker) { clearTimeout(timerResetPicker); } timerResetPicker =
     * setTimeout(function() { console.log('Reset picker');
     * bl_setDaterangepicker({ startDate: $("[name=sfrom]", formObject).val(),
     * endDate: $("[name=sto]", formObject).val() }); }, 100); });
     */

    function initLocationSelectors(provinceSelector, districtSelector, wardSelector) {
        if (!$(provinceSelector).length) {
            return;
        }

        var selProvince = $(provinceSelector);
        var selDistrict = districtSelector ? $(districtSelector) : null;
        var selWard = wardSelector ? $(wardSelector) : null;

        // Load ra tỉnh
        function loadProvince() {
            selProvince.prop('disabled', true);
            if (selDistrict) selDistrict.prop('disabled', true);
            if (selWard) selWard.prop('disabled', true);

            $.ajax({
                url: domain_load_remote + 'data/config/location-province-' + nv_lang_interface + '.json?t=' + selProvince.data('timestamp'),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="0">' + selProvince.data('default') + '</option>';
                    $.each(json, function(id, title) {
                        html += '<option value="' + id + '"' + (id == selProvince.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                    });
                    selProvince.html(html);
                    selProvince.prop('disabled', false);
                    selProvince.select2({
                        width: '100%',
                        placeholder: selProvince.data('default'),
                        allowClear: true,
                        language: nv_lang_interface
                    });
                    loadDistrict();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        // Load ra huyện
        function loadDistrict() {
            if (!selDistrict) return;
            selDistrict.prop('disabled', true);
            if (selWard) selWard.prop('disabled', true);

            if (selWard && $('#ward_container').length) {
                $('#ward_container').hide();
            }

            if (selProvince.val() <= 0) {
                selDistrict.data('selected', 0);
                if ($('#district_container').length) {
                    $('#district_container').hide();
                }

                selDistrict.prop('disabled', false);
                selDistrict.html('<option value="0">' + (selDistrict.data('default')) + '</option>').select2({
                    width: '100%',
                    placeholder: selDistrict.data('default'),
                    allowClear: true,
                    language: nv_lang_interface
                });
                loadWard();
                return;
            }

            if ($('#district_container').length) {
                $('#district_container').show();
            }

            $.ajax({
                url: domain_load_remote + 'data/config/location-district-' + nv_lang_interface + '.json?t=' + new Date().getTime(),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="0">' + (selDistrict.data('default')) + '</option>';
                    if (typeof json[selProvince.val()] != 'undefined') {
                        json = json[selProvince.val()];
                        $.each(json, function(id, title) {
                            html += '<option value="' + id + '"' + (id == selDistrict.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                        });
                    }
                    selDistrict.prop('disabled', false);
                    selDistrict.html(html).select2({
                        width: '100%',
                            placeholder: selDistrict.data('default'),
                            allowClear: true,
                        language: nv_lang_interface
                    });
                    loadWard();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        // Load ra xã
        function loadWard() {
            if (!selWard) return;

            selWard.prop('disabled', true);

            if (selDistrict.val() == 0) {
                selWard.data('selected', 0);

                if ($('#ward_container').length) {
                    $('#ward_container').hide();
                }

                selWard.prop('disabled', false);
                selWard.html('<option value="0">' + (selWard.data('default')) + '</option>').select2({
                    width: '100%',
                    placeholder: selWard.data('default'),
                    allowClear: true,
                    language: nv_lang_interface
                });
                return;
            }

            if ($('#ward_container').length) {
                $('#ward_container').show();
            }

            $.ajax({
                url: domain_load_remote + 'data/config/location-ward-' + nv_lang_interface + '.json?t=' + new Date().getTime(),
                cache: true,
                dataType: 'json',
                method: 'GET',
                success: function(json) {
                    var html = '<option value="0">' + (selWard.data('default')) + '</option>';
                    if (typeof json[selDistrict.val()] != 'undefined') {
                        json = json[selDistrict.val()];
                        $.each(json, function(id, title) {
                            html += '<option value="' + id + '"' + (id == selWard.data('selected') ? ' selected="selected"' : '') + '>' + title + '</option>';
                        });
                    }
                    selWard.prop('disabled', false);
                    selWard.html(html).select2({
                        width: '100%',
                        placeholder: selWard.data('default'),
                        allowClear: true,
                        language: nv_lang_interface
                    });
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR, textStatus, errorThrown);
                }
            });
        }

        if (selProvince.length && (!selDistrict || selDistrict.length) && (!selWard || selWard.length)) {
            loadProvince();

            if (selProvince.data('selected') > 0) {
                if ($('#district_container').length) {
                    $('#district_container').show();
                }
            }

            if (selDistrict && selDistrict.data('selected') > 0) {
                if ($('#ward_container').length) {
                    $('#ward_container').show();
                }
            }
        }

        selProvince.on('change', function() {
            loadDistrict();
        });

        if (selDistrict) {
            selDistrict.on('change', function() {
                loadWard();
            });
        }
    }

    if ($('#keyword_id_province').length) {
        initLocationSelectors('#keyword_id_province', '#keyword_id_district', null);
    }

    if ($('#rq_province').length) {
        initLocationSelectors('#rq_province', '#rq_district', '#rq_ward');
    }
});
function reset_validate() {
    price_active = [];
    $('#win_price_from, #win_price_to, #kqmt_price_plan_from, #kqmt_price_plan_to, #tbmt_price_from, #tbmt_price_to, #contract_price_plan_from, #contract_price_plan_to, #dev_invest_from, #dev_invest_to, #money_from, #money_to, #price_from, #price_to, #invest_from, #invest_to, #result_price_plan_to, #result_price_plan_from, #price_plan_from, #price_plan_to, #keyword_min_bid_prices, #keyword_max_bid_prices, #rq_category, #rq_investor').val('');
    $('#win_price_from, #win_price_to, #kqmt_price_plan_from, #kqmt_price_plan_to, #tbmt_price_from, #tbmt_price_to, #contract_price_plan_from, #contract_price_plan_to, #dev_invest_from, #dev_invest_to, #money_from, #money_to, #price_from, #price_to, #invest_from, #invest_to, #result_price_plan_to, #result_price_plan_from, #price_plan_from, #price_plan_to, #keyword_min_bid_prices, #keyword_max_bid_prices').tooltip("destroy");
    $("button:contains('Tìm kiếm')").prop("disabled", false);
    $("input[value='Tìm kiếm']").prop("disabled", false);
    // $("button:contains('Tìm kiếm')").tooltip("destroy");
}
function validate_money(input_first, input_second) {
    var money_from = $('#' + input_first + '');
    var money_to = $('#' + input_second + '');
    if (money_from.val() && money_to.val()) {
        var i_money_from = formatNumber(money_from.val());
        var i_money_to = formatNumber(money_to.val());
        if (i_money_from >= i_money_to) {
            if (price_active.indexOf(input_first) === -1) {
                price_active.push(input_first);
            }
            $(money_from).tooltip({'title': 'Vui lòng nhập khoảng giá hợp lệ (giá từ phải nhỏ hơn giá đến) hoặc để trống'});
            $(money_from).tooltip({
                trigger: 'manual'
            }).tooltip("show");
            $("button:contains('Tìm kiếm')").prop("disabled", true);
            // $("button:contains('Tìm kiếm')").tooltip({placement: 'bottom','title': 'Vui lòng nhập khoảng giá hợp lệ hoặc để trống'});
            $("button:contains('Tìm kiếm')").tooltip({
                trigger: 'hover'
            }).tooltip("show");
            $("input[value='Tìm kiếm']").prop("disabled", true);
        } else {
            var index = price_active.indexOf(input_first);
            if (index !== -1) {
                $('#' + price_active[index] + '').tooltip("destroy");
                price_active.splice(index, 1);
            }
            if (price_active.length === 0) {
                $(money_from).tooltip("destroy");
                // $("button:contains('Tìm kiếm')").tooltip("destroy");
                $("button:contains('Tìm kiếm')").prop("disabled", false);
                $("input[value='Tìm kiếm']").prop("disabled", false);
            }
        }
    } else {
        var index = price_active.indexOf(input_first);
        if (index !== -1) {
            $('#' + price_active[index] + '').tooltip("destroy");
            price_active.splice(index, 1);
        }
        if (price_active.length === 0) {
            // $(money_from).tooltip("destroy");
            $("button:contains('Tìm kiếm')").prop("disabled", false);
            $("button:contains('Tìm kiếm')").tooltip("destroy");
            $("input[value='Tìm kiếm']").prop("disabled", false);
        }
    }
}
function formatNumber(num) {
    if (num) {
        let output = parseFloat(num.replace(/,/g, ''));
        return output;
    }
    return 0;
}
function getFormValue(name) {
    return $('[name=' + name + ']', formObject).val();
}

function getIntFormValue(name) {
    return parseInt(getFormValue(name));
}

function getSelectedAliases() {
    return $('#idprovince option:selected').map(function() {
        return $(this).data('alias');
    }).get();
}

function getSelectedFields() {
    return $('input[name="field[]"]:checked').map(function() {
        return $(this).val();
    }).get();
}

function getSelectedPhuongthuc() {
    return $('input[name="phuong_thuc[]"]:checked').map(function() {
        return $(this).val();
    }).get();
}

function setFormAction(actionUrl, isProvince) {
    formObject.attr("action", actionUrl);
    $('input[name="is_province"]', formObject).val(isProvince);
}

function setFormAction_KQLCNT(actionUrl, isKQLCNT) {
    formObject.attr("action", actionUrl);
    $('input[name="is_kqlcnt"]', formObject).val(isKQLCNT);
}

function getSelectedRadio(name) {
    return $('input[name=' + name + ']:checked', formObject).val();
}

$('input[name="type_kqlcnt"]').on('change', function() {
    const type_kqlcnt = parseInt($(this).val()) || 0;
    $('input[name="is_kqlcnt"]', formObject).val(type_kqlcnt > 0 ? 1 : 0);
});

function updateDateRangeInputs() {
    const dateRange = $('.search_range').val();
    const dates = dateRange.split(' - ');
    const defaultDateRange = $('.search_range').data('default').split(' - ');

    if (dates.length === 2) {
        $('input[name="sfrom"]').val(dates[0]);
        $('input[name="sto"]').val(dates[1]);
        return dates[0] !== defaultDateRange[0] || dates[1] !== defaultDateRange[1];
    }
    return false;
}

function updateFormAction() {

    const selectedAliases = getSelectedAliases();
    const selectedFields = getSelectedFields();
    const selectedPhuongthuc = getSelectedPhuongthuc();
    const searchkind_all = getSelectedRadio('searchkind');
    const goods = getSelectedRadio('goods');
    const q = getFormValue('q');
    const q2 = getFormValue('q2');
    const without_key = getFormValue('without_key');
    const type_info = getIntFormValue('type_info');
    const type_search = getIntFormValue('type_search');
    const type_info3 = getIntFormValue('type_info3');
    const type_choose_id = getIntFormValue('type_choose_id');
    const goods_2 = getIntFormValue('goods_2');
    const type_view_open = getIntFormValue('type_view_open');
    const sl_nhathau = getIntFormValue('sl_nhathau');
    const idregion = getIntFormValue('idregion');
    const type_org = getSelectedRadio('type_org');
    const keyword_id_bidder = getIntFormValue('keyword_id_bidder');
    const keyword_id_province = getIntFormValue('keyword_id_province');
    const keyword_id_district = getIntFormValue('keyword_id_district');
    const khlcnt = getIntFormValue('khlcnt');
    const dateRangeChanged = updateDateRangeInputs();
    const money_from = getFormValue('money_from');
    const money_to = getFormValue('money_to');
    const price_from = getFormValue('price_from');
    const price_to = getFormValue('price_to');
    const invest_from = getFormValue('invest_from');
    const invest_to = getFormValue('invest_to');

    if (type_info === 1 && type_search === 1) {
        // Kiểm tra điều kiện để redirect đến URL SEO tỉnh thành
        const conditionsMet = selectedAliases.length === 1 &&
            selectedAliases[0] !== '' &&
            q === '' && q2 === '' && without_key === '' &&
            type_info3 === 1 && type_choose_id === 0 && goods_2 === 0 &&
            type_view_open === 0 && sl_nhathau === 0 && idregion === 0 &&
            type_org === '1' && goods === '0' &&
            keyword_id_bidder === 0 && keyword_id_province === 0 &&
            keyword_id_district === 0 && khlcnt === 0 &&
            selectedFields.length === 0 && selectedPhuongthuc.length === 0 &&
            searchkind_all === '0' && !dateRangeChanged &&
            money_from === '' && money_to === '' &&
            price_from === '' && price_to === '' &&
            invest_from === '' && invest_to === '';
        if (conditionsMet) {
            const provinceAlias = selectedAliases[0];
            const defaultSFrom = $('input[name="sfrom"]').data('default');
            const defaultSTo = $('input[name="sto"]').data('default');
            const actionUrl = formObject.data("action16") + encodeURIComponent(provinceAlias) +
                "?sfrom=" + encodeURIComponent(defaultSFrom) + "&sto=" + encodeURIComponent(defaultSTo) + "&is_advance=1";
            setFormAction(actionUrl, 1);
        } else {
            var actionUrl = formObject.data("action11");
            setFormAction(actionUrl, 0);
        }
    }
}

function updateFormAction_KQLCNT() {
    const searchkind_all = getSelectedRadio('searchkind');
    const goods = getSelectedRadio('goods');
    const q = getFormValue('q');
    const q2 = getFormValue('q2');
    const without_key = getFormValue('without_key');
    const type_info = getIntFormValue('type_info');
    const type_search = getIntFormValue('type_search');
    const type_info3 = getIntFormValue('type_info3');
    const type_choose_id = getIntFormValue('type_choose_id');
    const goods_2 = getIntFormValue('goods_2');
    const dateRangeChanged = updateDateRangeInputs();
    const result_price_plan_from = getFormValue('result_price_plan_from');
    const result_price_plan_to = getFormValue('result_price_plan_to');
    const win_price_from = getFormValue('win_price_from');
    const win_price_to = getFormValue('win_price_to');
    const type_kqlcnt = parseInt($('input[name="type_kqlcnt"]:checked').val());

    if (type_info === 3) {
        const conditionsMet = q === '' && q2 === '' && without_key === '' &&
            type_search === 1 && type_info3 === 1 &&
            type_choose_id === 0 && goods_2 === 0 &&
            goods === '0' && searchkind_all === '0' &&
            !dateRangeChanged && result_price_plan_from === '' &&
            result_price_plan_to === '' && win_price_from === '' &&
            win_price_to === '';
        if (conditionsMet) {
            const defaultSFrom = $('input[name="sfrom"]').data('default');
            const defaultSTo = $('input[name="sto"]').data('default');

            let actionUrl;
            if (type_kqlcnt === 1) {
                actionUrl = formObject.data("action18") + "?sfrom=" + encodeURIComponent(defaultSFrom) + "&sto=" + encodeURIComponent(defaultSTo) + "&is_advance=1";
                setFormAction_KQLCNT(actionUrl, 1);

            } else if (type_kqlcnt === 2) {
                actionUrl = formObject.data("action19") + "?sfrom=" + encodeURIComponent(defaultSFrom) + "&sto=" + encodeURIComponent(defaultSTo) + "&is_advance=1";
                setFormAction_KQLCNT(actionUrl, 1);
            } else {
                const actionUrl = formObject.data("action20");
                setFormAction_KQLCNT(actionUrl, 0);
            }
        } else {
            const actionUrl = formObject.data("action20");
            setFormAction_KQLCNT(actionUrl, 0);
        }
    }
}

$('#idprovince').on('change', updateFormAction);
$('[name=q], [name=q2], [name=without_key], [name=idregion]', formObject).on('input', updateFormAction);
$('input[name="field[]"], input[name="phuong_thuc[]"], input[name="bidfieid[]"], input[name=type_org], input[name="searchkind"], input[name="goods"]').on('change', updateFormAction);
$('.search_range').on('change', updateFormAction);

if (parseInt($('[name=type_info]').val()) === 3) {
    $('[name=type_kqlcnt], input[name="bidfieid[]"]').on('change', updateFormAction_KQLCNT);
    $('[name=q], [name=q2], [name=without_key], [name=idregion], [name=type_org]', formObject).on('input', updateFormAction_KQLCNT);
    $('.search_range').on('change', updateFormAction_KQLCNT);
}

$('input[name="type_kqlcnt"]').on('change', function() {
    const searchkind_all = getSelectedRadio('searchkind');
    const goods = getSelectedRadio('goods');
    const q = getFormValue('q');
    const q2 = getFormValue('q2');
    const without_key = getFormValue('without_key');
    const type_info = getIntFormValue('type_info');
    const type_search = getIntFormValue('type_search');
    const type_info3 = getIntFormValue('type_info3');
    const type_choose_id = getIntFormValue('type_choose_id');
    const goods_2 = getIntFormValue('goods_2');
    const dateRangeChanged = updateDateRangeInputs();
    const result_price_plan_from = getFormValue('price_plan_from');
    const result_price_plan_to = getFormValue('price_plan_to');
    const win_price_from = getFormValue('win_price_from');
    const win_price_to = getFormValue('win_price_to');
    const type_kqlcnt = parseInt($('input[name="type_kqlcnt"]:checked').val());

    if (type_info === 3) {
        const conditionsMet = q === '' && q2 === '' && without_key === '' &&
            type_search === 1 && type_info3 === 1 &&
            type_choose_id === 0 && goods_2 === 0 &&
            searchkind_all === '0' &&
            !dateRangeChanged && result_price_plan_from === '' &&
            result_price_plan_to === '' && win_price_from === '' &&
            win_price_to === '';
        console.log(conditionsMet);
        const isKQLCNT = (conditionsMet && type_kqlcnt > 0) ? 1 : 0;
        console.log(isKQLCNT);
        $('input[name="is_kqlcnt"]', formObject).val(isKQLCNT);
    }
});

$('.submit_search').click(function(e) {
    const selectedAliases = getSelectedAliases();
    const is_province = parseInt($('[name=is_province]', formObject).val());
    const type_info = getIntFormValue('type_info');
    const type_kqlcnt = parseInt($('input[name="type_kqlcnt"]:checked').val());
    const is_kqlcnt = parseInt($('[name=is_kqlcnt]', formObject).val());

    if (is_kqlcnt === 1 && type_info === 3) {
        const defaultSFrom = $('input[name="sfrom"]').data('default');
        const defaultSTo = $('input[name="sto"]').data('default');
        let actionUrl;
        if (type_kqlcnt === 1) {
            actionUrl = formObject.data("action18") + "?sfrom=" + encodeURIComponent(defaultSFrom) + "&sto=" + encodeURIComponent(defaultSTo) + "&is_advance=1";
        } else if (type_kqlcnt === 2) {
            actionUrl = formObject.data("action19") + "?sfrom=" + encodeURIComponent(defaultSFrom) + "&sto=" + encodeURIComponent(defaultSTo) + "&is_advance=1";
        }
        window.location.href = actionUrl;
        return false;
    }

    // Xử lý các trường hợp khác (code cũ)
    if (is_province === 1 && type_info === 1) {
        const provinceAlias = selectedAliases[0];
        const defaultSFrom = $('input[name="sfrom"]').data('default');
        const defaultSTo = $('input[name="sto"]').data('default');
        const actionUrl = formObject.data("action16") + encodeURIComponent(provinceAlias) + "?sfrom=" + encodeURIComponent(defaultSFrom) + "&sto=" + encodeURIComponent(defaultSTo) + "&is_advance=1";
        window.location.href = actionUrl;
        return false;
    } else {
        $('input[name="is_province"]', formObject).val();
    }
});
