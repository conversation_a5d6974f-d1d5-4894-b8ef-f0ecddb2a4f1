<!-- BEGIN: main -->
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/{TEMPLATE}/js/bidding.js"></script>
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/{TEMPLATE}/js/block_search.js"></script>
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<link type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css" rel="stylesheet" />
<!-- BEGIN: css -->
<link type="text/css" rel="stylesheet" href="{NV_BASE_SITEURL}themes/{BLOCK_CSS}/css/{MODULE_FILE}.css" />
<!-- END: css -->
<div class="ltablesearch-cont">
    <form class="ltablesearch block-bidding" id="bltablesearch{BLOCKID}" action="{ACTION}" method="get" onsubmit="return bl_checkSearchForm(this);" data-id="{BLOCKID}" data-action="{FORM_ACTION}" data-action1="{FORM_ACTION1}" data-action2="{FORM_ACTION2}" data-action3="{FORM_ACTION3}" data-action4="{FORM_ACTION4}" data-action5="{FORM_ACTION5}" data-action6="{FORM_ACTION6}" data-action7="{FORM_ACTION7}" data-action8="{FORM_ACTION8}" data-action9="{FORM_ACTION9}" data-action10="{FORM_ACTION10}" data-action11="{FORM_ACTION11}" data-action15="{FORM_ACTION15}" data-action16="{FORM_ACTION16}"  data-action17="{FORM_ACTION17}"  data-action18="{FORM_ACTION18}" data-action19="{FORM_ACTION19}" data-action20="{FORM_ACTION20}" data-action21="{FORM_ACTION21}"  data-action22="{FORM_ACTION22}" data-action23="{FORM_ACTION23}" data-action42="{FORM_ACTION42}" data-maxspan="{MAXSPAN}" data-mindate="{MINDATE}" data-opens="left">
        <!-- BEGIN: no_rewrite -->
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" data-default="{NV_LANG_DATA}" /> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" data-default="{MODULE_NAME}" /> <input type="hidden" name="{NV_OP_VARIABLE}" value="search" data-default="search" />
        <!-- END: no_rewrite -->
        <div class="row block margin-bottom-lg">
            <div class="col-sm-24 col-md-24">
                <div class="row">
                    <div class="col-xs-24">
                        <div class="form-group">
                            <h2>{LANG.search_block_title}</h2>
                            <!-- BEGIN: show_hashtag -->
                            <div class="tag__search">
                                <a href="{LINK_DAUTHAU}#bcontent">{LANG.tag_search_dauthau}</a>
                                <a href="{LINK_DAUTHAU}?type_search=2#bcontent">{LANG.tag_search_dautu}</a>
                            </div>
                            <!-- END: show_hashtag -->
                            <div class="h3 visible-sm-block visible-md-block visible-lg-block visible-xl-block lower-case">{LANG.search_block_subtitle}</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-24 col-sm-15">
                        <div class="form-group">
                            <label class="control-label">{LANG.s_key} <span id="main_keynote" data-toggle="tooltip" data-original-title="{LANG.main_keynote}"><i class="fa fa-info-circle" aria-hidden="true"></i></span></label>
                            <div class="row">
                                <div class="col-xs-24">
                                    <div class="input-group">
                                        <input class="form-control" id="ls_key_bidding" type="text" name="q" value="{Q}" data-default="" maxlength="200" data-error="{LANG.type_text_error}" />
                                        <div class="input-group-btn">
                                            <input class="btn btn-primary submit_search" type="submit" value="{LANG.search}" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-24 col-sm-9">
                        <!-- BEGIN: type_search12 -->
                        <div class="form-group">
                            <label class="control-label">{LANG.type_search}</label>
                            <div class="display-flex">
                                <label class="custom-radio-inline">
                                    <input type="radio" name="type_search" value="1" {TYPE_SEARCH1}><span class="txt">{LANG.type_search1}</span>
                                </label>
                                <label class="custom-radio-inline">
                                    <input type="radio" name="type_search" value="2" {TYPE_SEARCH2}><span class="txt">{LANG.type_search2}</span>
                                </label>
                            </div>
                        </div>
                        <!-- END: type_search12 -->

                        <!-- BEGIN: type_search3 -->
                        <input type="radio" class="hidden" name="type_search" value="3" {TYPE_SEARCH3}>
                        <!-- END: type_search3 -->

                        <div class="form-group">
                            <label class="custom-checkbox toggle par_search" data-par-search="{LANG.par_search}" data-par-search2="{LANG.par_search2}">
                                <input class="form-control" type="checkbox" name="par_search" data-default="0" value="1" {PAR_SEARCH} /><span class="txt">{LANG_PAR_SEARCH}</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-24 col-sm-15">
                        <div class="row">
                            <div class="form-group col-md-14">
                                <label class="control-label">{LANG.type_info}</label> <select class="form-control" onchange="bl_changeTypeInfo()" name="type_info"
                                    <!-- BEGIN: type_info_hide --> style="display: none"
                                    <!-- END: type_info_hide -->>
                                    <!-- BEGIN: type_info -->
                                    <option value="{TYPE.key}"{TYPE.selected}>{TYPE.title}</option>
                                    <!-- END: type_info -->
                                </select> <select class="form-control" onchange="bl_changeTypeInfo2()" name="type_info2"
                                    <!-- BEGIN: type_info2_hide --> style="display: none"
                                    <!-- END: type_info2_hide -->>
                                    <!-- BEGIN: type_info2 -->
                                    <option value="{TYPE2.key}"{TYPE2.selected}>{TYPE2.title}</option>
                                    <!-- END: type_info2 -->
                                </select> <select class="form-control" onchange="bl_changeTypeInfo3()" name="type_info3"
                                    <!-- BEGIN: type_info3_hide --> style="display: none"
                                    <!-- END: type_info3_hide -->>
                                    <!-- BEGIN: type_info3 -->
                                    <option value="{TYPE3.key}"{TYPE3.selected}>{TYPE3.title}</option>
                                    <!-- END: type_info3 -->
                                </select>
                                <select class="form-control {OP}" name="ketqua_luachon_tochuc_dgts" data-default="0">
                                    <!-- BEGIN: kqlctc_dgts -->
                                    <option value="{kqlctc_dgts.key}" {kqlctc_dgts.selected}>{kqlctc_dgts.title}</option>
                                    <!-- END: kqlctc_dgts -->
                                </select>
                            </div>
                            <div class="form-group col-md-10">
                                <label class="control-label">{LANG.search_range}</label>
                                <div class="row">
                                    <div class="col-xs-24">
                                        <input type="hidden" name="sfrom" value="{FROM}" data-default="{FROM_DEFAULT}" /> <input type="hidden" name="sto" value="{TO}" data-default="{TO_DEFAULT}" /> <input class="form-control search_range" type="text" value="{FROM} - {TO}" data-default="{FROM_DEFAULT} - {TO_DEFAULT}" data-cancel-lang="{GLANG.cancel}" data-apply-lang="{GLANG.apply}" data-customrange-lang="{GLANG.custom_range}" data-today-lang="{LANG.today}" data-lastday-lang="{LANG.last_1_day}" data-last7days-lang="{LANG.last_7_days}" data-last14days-lang="{LANG.last_14_days}" data-last30days-lang="{LANG.last_30_days}" data-thismonth-lang="{LANG.this_month}" data-lastmonth-lang="{LANG.last_month}" data-last3months-lang="{LANG.last_3_months}" data-last6months-lang="{LANG.last_6_months}" data-last-maxspan-lang="{LAST_MAXSPAN_LANG}" data-lastall="{LAST_ALL_DAYS}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-24 col-sm-9">
                        <div class="row">
                            <div class="col-xs-24">
                                <input type="hidden" name="is_advance" value="{ADVANCE}" data-default="0" />
                                <input type="hidden" name="is_province" value="{is_province}" data-default="0" />
                                <input type="hidden" name="is_kqlcnt" value="{is_kqlcnt}" data-default="0" />
                                <div class="panel panel-default"
                                    <!-- BEGIN: advance_bl_hide -->
                                    style="display: none"
                                    <!-- END: advance_bl_hide -->
                                    > <a class="panel-heading btn-search"
                                        <!-- BEGIN: advance_btn_hide --> style="display: none"<!-- END: advance_btn_hide --> href="javascript:void(0);" data-search-simple="{LANG.search_simple}" data-search-advance="{LANG.search_advance}" data-icon-search-simple="icon-chevron-down" data-icon-search-advance="icon-chevron-up"><em class="<!-- BEGIN: advance_icon_0 -->icon-chevron-down <!-- END: advance_icon_0 --><!-- BEGIN: advance_icon_1 -->icon-chevron-up <!-- END: advance_icon_1 -->margin-right-sm"></em><strong class="txt">{LANG_ADVANCE}</strong></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="panel-body advance-search"
                        <!-- BEGIN: advance_hide -->
                        style="display: none"
                        <!-- END: advance_hide -->
                        >
                        <div id="show_key">
                            <div class="row">
                                <div class="col-md-15">
                                    <div class="form-group">
                                        <label class="control-label">{LANG.s_key2} <span data-toggle="tooltip" data-original-title="{LANG.note_key2}"><i class="fa fa-info-circle" aria-hidden="true"></i></span></label>
                                        <div class="row">
                                            <div class="col-xs-24">
                                                <input class="form-control" id="ls_key2" type="text" name="q2" value="{Q2}" data-default="" maxlength="200" data-error="{LANG.type_text_error}" />
                                            </div>
                                        </div>
                                        <div class="row margin-top-sm">
                                            <div class="col-xs-24">
                                                <label class="custom-checkbox toggle"> <input class="form-control" type="checkbox" name="search_one_key" value="1" data-default="false" {SEARCH_ONE_KEY} /><span class="txt">{LANG.one_key}</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="control-label">{LANG.without_key} <span data-toggle="tooltip" data-original-title="{LANG.note_withoutkey}"><i class="fa fa-info-circle" aria-hidden="true"></i></span></label>
                                        <div class="row">
                                            <div class="col-xs-24">
                                                <input class="form-control" id="without_key" type="text" name="without_key" value="{Q1}" data-default="" maxlength="200" data-error="{LANG.type_text_error}" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group htluachon_filter"
                                        <!-- BEGIN: ls_htluachon_hide -->
                                        style="display: none"
                                        <!-- END: ls_htluachon_hide -->>
                                        <label class="control-label">{LANG.type_choose}:</label>
                                        <div class="row">
                                            <div class="col-sm-24">
                                                <select class="form-control" id="ls_cat" name="type_choose_id" data-default="0">
                                                    <option value="0">{LANG.all_htlc}</option>
                                                    <!-- BEGIN: htluachon -->
                                                    <option value="{HTLC_KEY}"{HTLC_SELECTED}>{HTLC_TITLE}</option>
                                                    <!-- END: htluachon -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group htluachontb_filter"
                                        <!-- BEGIN: ls_htluachon_tb_hide -->
                                        style="display: none"
                                        <!-- END: ls_htluachon_tb_hide -->>
                                        <label class="control-label">{LANG.type_choose}:</label>
                                        <div class="row">
                                            <div class="col-sm-24">
                                                <select class="form-control" id="ls_cat" name="type_choose_id" data-default="0">
                                                    <option value="0">{LANG.all_htlc}</option>
                                                    <!-- BEGIN: htluachontb -->
                                                    <option value="{HTLC_KEY}"{HTLC_SELECTED}>{HTLC_TITLE}</option>
                                                    <!-- END: htluachontb -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group provinceid_kqlcnt"
                                        <!-- BEGIN: hide_province_kqlcnt -->
                                        style="display: none"
                                        <!-- END: hide_province_kqlcnt -->>
                                        <label class="control-label">{LANG.idprovince}</label>
                                        <div class="row">
                                            <div class="col-xs-24">
                                                <select class="form-control fselect2 selected_phanmuc"
                                                        id="idprovincekq" name="idprovincekq[]" multiple="multiple" style="width: 100%">
                                                    <!-- BEGIN: loopidprovince_2 -->
                                                    <option data-alias="{PROVINCE.alias}" value="{PROVINCE.key}"{PROVINCE.selected}>{PROVINCE.title}</option>
                                                    <!-- END: loopidprovince_2 -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group provinceid_khttlcnt"
                                        <!-- BEGIN: hide_province_khttlcnt -->
                                        style="display: none"
                                        <!-- END: hide_province_khttlcnt -->>
                                        <label class="control-label">{LANG.idprovince}</label>
                                        <div class="row">
                                            <div class="col-xs-24">
                                                <select class="form-control fselect2 selected_phanmuc"
                                                        id="idprovince_khtt" name="idprovince_khtt[]" multiple="multiple" style="width: 100%">
                                                    <!-- BEGIN: loopidprovince_3 -->
                                                    <option value="{PROVINCE_KHTT.key}"{PROVINCE_KHTT.selected}>{PROVINCE_KHTT.title}</option>
                                                    <!-- END: loopidprovince_3 -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group search_phanmuc"
                                        <!-- BEGIN: hide_phanmuc -->
                                        style="display: none"
                                        <!-- END: hide_phanmuc -->
                                    >
                                        <label class="control-label" >{LANG.phan_muc}</label>
                                        <div class="row">
                                            <div class="col-xs-24">
                                                <select class="form-control fselect2 selected_phanmuc" id="phanmucid" name="phanmucid[]" multiple="multiple" style="width: 100%">
                                                    <!-- BEGIN: loopphanmuc1 -->
                                                    <optgroup label="{PHANMUC_MAIN.title}">
                                                        <!-- BEGIN: loopphanmuc2 -->
                                                        <option value="{PHANMUC_SUB.key}"{PHANMUC_SUB.selected}>{PHANMUC_SUB.title}</option>
                                                        <!-- END: loopphanmuc2 -->
                                                    </optgroup>
                                                    <!-- END: loopphanmuc1 -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="search_result_block"
                                        <!-- BEGIN: no_result -->
                                        style="display: none"
                                        <!-- END: no_result -->
                                    >
                                        <div class="form-group">
                                            <label class="control-label">{LANG.price_contract}</label>
                                        </div>
                                        <div class="form-group col-md-12 pdl0">
                                            <input class="money" type="hidden" name="price_plan_from" value="{PRICE_PLAN_FROM}" data-default="" />
                                            <div class="input-group">
                                                <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="result_price_plan_from" type="text" data-name="price_plan_from" value="{PRICE_PLAN_FROM_FORMAT}" data-default="" maxlength="20" />
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12 pdl0">
                                            <input class="money" type="hidden" name="price_plan_to" value="{PRICE_PLAN_TO}" data-default="" />
                                            <div class="input-group">
                                                <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="result_price_plan_to" type="text" data-name="price_plan_to" value="{PRICE_PLAN_TO_FORMAT}" data-default="" maxlength="20" />
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label">{LANG.win_price}</label>
                                        </div>
                                        <div class="form-group col-md-12 pdl0">
                                            <input class="money" type="hidden" name="win_price_from" value="{WIN_PRICE_PROM}" data-default="" />
                                            <div class="input-group">
                                                <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="win_price_from" type="text" data-name="win_price_from" value="{WIN_PRICE_FROM_FORMAT}" data-default="" maxlength="20" />
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12 pdl0">
                                            <input class="money" type="hidden" name="win_price_to" value="{WIN_PRICE_TO}" data-default="" />
                                            <div class="input-group">
                                                <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="win_price_to" type="text" data-name="win_price_to" value="{WIN_PRICE_TO_FORMAT}" data-default="" maxlength="20" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="form-group"
                                        <!-- BEGIN: search_type_content -->
                                        style="display: none"
                                        <!-- END: search_type_content -->
                                        >
                                        <div class="row">
                                            <div class="col-xs-24">
                                                <label class="custom-checkbox toggle"> <input class="form-control" type="checkbox" name="search_type_content" value="1" data-default="false" {SEARCH_TYPE_CONTENT} /><span class="txt">{LANG.search_type_sign}</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group goods_kqlcnt"
                                        <!-- BEGIN: hide_goods_kqlcnt -->
                                        style="display: none"
                                        <!-- END: hide_goods_kqlcnt -->>
                                        <label class="control-label">{LANG.goods_search}</label>
                                        <!-- BEGIN: goods_2 -->
                                        <label class="custom-radio"> <input type="radio" name="goods_2" value="{GOODS2.key}" data-default="0"{GOODS2.checked}><span class="txt">{GOODS2.title}</span>
                                        </label>
                                        <!-- END: goods_2 -->
                                    </div>
                                    <div class="form-group search_phanmuc"
                                        <!-- BEGIN: hide_tbmt_open_only -->
                                        style="display: none"
                                        <!-- END: hide_tbmt_open_only -->
                                        >
                                        <div class="row">
                                            <div class="col-xs-24">
                                                <label class="custom-checkbox toggle"> <input class="form-control" type="checkbox" name="open_only" value="1" data-default="false" {OPEN_ONLY} /><span class="txt">{LANG.tbmt_open_only}</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group"<!-- BEGIN: search_kind -->
                                        style="display: none"
                                        <!-- END: search_kind -->>
                                        <label class="control-label">{LANG.searchkind}</label>
                                        <!-- BEGIN: kind -->
                                        <label class="custom-radio"> <input type="radio" name="searchkind" value="{KIND.key}" data-default="0"{KIND.checked}><span class="txt">{KIND.title}</span>
                                        </label>
                                        <!-- END: kind -->
                                    </div>

                                    <div class="form-group search_result_block"
                                        <!-- BEGIN: no_result -->
                                        style="display: none"
                                        <!-- END: no_result -->
                                        > <label class="control-label">{LANG.cat}</label>
                                        <!-- BEGIN: catressult -->
                                        <label class="custom-radio"> <input type="radio" name="catressult" value="{CAT.key}" data-default="0"{CAT.checked_result}><span class="txt">{CAT.title}</span>
                                        </label>
                                        <!-- END: catressult -->
                                    </div>

                                    <div class="form-group search_result_block search_type_kqlcnt"
                                        <!-- BEGIN: hide_type_kqlcnt_link -->
                                        style="display: none"
                                        <!-- END: hide_type_kqlcnt_link -->
                                        > <label class="control-label">{LANG.title_type_kqlcnt_search}</label>
                                        <!-- BEGIN: type_kqlcnt_link -->
                                        <label class="custom-radio"> <input type="radio" name="type_kqlcnt" value="{TYPE_KQLCNT.key}" data-default="0"{TYPE_KQLCNT.checked}><span class="txt">{TYPE_KQLCNT.title}</span>
                                        </label>
                                        <!-- END: type_kqlcnt_link -->
                                    </div>

                                    <div class="form-group search_bidfieid" <!-- BEGIN: hide_bidfieid --> style="display: none;" <!-- END: hide_bidfieid --> >
                                        <label class="control-label">{LANG.field}</label>
                                        <!-- BEGIN: bidfieid -->
                                        <div id="bidfieid_{BIDFIEID.key}">
                                            <label class="custom-checkbox">
                                                <input type="checkbox" id="kqlcnt_field_block_{BIDFIEID.key}" name="bidfieid[]" class="kqlcnt_field" value="{BIDFIEID.key}" data-default="false"{BIDFIEID.checked}>
                                                <span class="txt">{BIDFIEID.title}</span>
                                            </label>
                                        </div>
                                        <!-- END: bidfieid -->
                                    </div>

                                </div>
                            </div>
                            <div class="search_kqmt"<!-- BEGIN: no_kqmt --> style="display: none" <!-- END: no_kqmt -->>
                                <div class="row">
                                    <div class="col-md-15">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.status_open}</label>
                                            <select class="form-control" name="type_view_open" id="type_open" data-default="0">
                                                <!-- BEGIN: type_view_open -->
                                                <option value="{TYPE_OPEN.key}"{TYPE_OPEN.selected}>{TYPE_OPEN.title}</option>
                                                <!-- END: type_view_open -->
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label">{LANG.sl_nhathau}:</label>
                                            <div class="row">
                                                <div class="col-sm-24">
                                                    <select class="form-control" name="sl_nhathau" data-default="0">
                                                        <!-- BEGIN: sl_nhathau -->
                                                        <option value="{SL_NHATHAU.key}" {SL_NHATHAU.selected}>{SL_NHATHAU.title}</option>
                                                        <!-- END: sl_nhathau -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label">{LANG.price_contract}:</label>
                                            <div class="row">
                                                <div class="form-group col-md-12">
                                                    <input class="money" type="hidden" name="price_plan_from" value="{PRICE_PLAN_FROM}" data-default="" />
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="price_plan_from" type="text" data-name="price_plan_from" value="{PRICE_PLAN_FROM_FORMAT}" data-default="" maxlength="20" />
                                                    </div>
                                                </div>
                                                <div class="form-group col-md-12">
                                                    <input class="money" type="hidden" name="price_plan_to" value="{PRICE_PLAN_TO}" data-default="" />
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="price_plan_to" type="text" data-name="price_plan_to" value="{PRICE_PLAN_TO_FORMAT}" data-default="" maxlength="20" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                     <div class="col-md-9">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.field}</label>
                                            <div class="">
                                                <!-- BEGIN: field_kqmt -->
                                                <label class="custom-checkbox"
                                                    <!-- BEGIN: field_hide --> style="display: none"<!-- END: field_hide -->> <input type="checkbox" id="ccb_field_block_{FIELD.key}" name="field_kqmt[]" class="ccb_field" value="{FIELD.key}" data-default="false"{FIELD.checked_kqmt}><span class="txt">{FIELD.title}</span>
                                                </label>
                                                <!-- END: field_kqmt -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="search_kqcgtt"<!-- BEGIN: no_kqcgtt --> style="display: none" <!-- END: no_kqcgtt -->>
                                <div class="row">
                                    <div class="col-md-15">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.sl_nhathau}:</label>
                                            <div class="row">
                                                <div class="col-sm-24">
                                                    <select class="form-control" name="sl_nhathau_cgtt" data-default="0">
                                                        <!-- BEGIN: sl_nhathau_cgtt -->
                                                        <option value="{SL_NHATHAU.key}" {SL_NHATHAU.selected_cgtt}>{SL_NHATHAU.title}</option>
                                                        <!-- END: sl_nhathau_cgtt -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label">{LANG.price_contract}:</label>
                                            <div class="row">
                                                <div class="form-group col-md-12">
                                                    <input class="money" type="hidden" name="price_plan_from" value="{PRICE_PLAN_FROM}" data-default="" />
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="price_plan_from" type="text" data-name="price_plan_from" value="{PRICE_PLAN_FROM_FORMAT}" data-default="" maxlength="20" />
                                                    </div>
                                                </div>
                                                <div class="form-group col-md-12">
                                                    <input class="money" type="hidden" name="price_plan_to" value="{PRICE_PLAN_TO}" data-default="" />
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="price_plan_to" type="text" data-name="price_plan_to" value="{PRICE_PLAN_TO_FORMAT}" data-default="" maxlength="20" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                     <div class="col-md-9">
                                        <div class="form-group">
                                            <label class="control-label">{LANG.field}</label>
                                            <div class="">
                                                <!-- BEGIN: field_kqcgtt -->
                                                <label class="custom-checkbox"
                                                    <!-- BEGIN: field_hide --> style="display: none"<!-- END: field_hide -->> <input type="checkbox" id="ccb_field_block_{FIELD.key}" name="field_kqcgtt[]" class="ccb_field" value="{FIELD.key}" data-default="false"{FIELD.checked_kqcgtt}><span class="txt">{FIELD.title}</span>
                                                </label>
                                                <!-- END: field_kqcgtt -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="search_tbmt"
                                <!-- BEGIN: no_tbmt -->
                                style="display: none"
                                <!-- END: no_tbmt -->
                                >
                                <div class="row">
                                    <div class="col-md-15">
                                        <div class="form-group search_phanmuc"
                                            <!-- BEGIN: hide_vsic -->
                                            style="display: none"
                                            <!-- END: hide_vsic -->
                                        >
                                            <label class="control-label" >{LANG.vsic}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <select class="form-control fselect2 selected_phanmuc" id="listvsic" name="vsic[]" multiple="multiple" style="width: 100%">
                                                        <!-- BEGIN: loopvsic -->
                                                        <option value="{VSIC.code}"{VSIC.selected}><!-- BEGIN: is_code -->{VSIC.code} - <!-- END: is_code -->{VSIC.title}</option>
                                                        <!-- END: loopvsic -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group province_sel">
                                            <label class="control-label">{LANG.idregion}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <select data-selected="{SREGION}" class="form-control selRegions" onchange="bl_changeLocationRegion()"
                                                            name="idregion" style="width: 100%" data-default="0">
                                                        <!-- BEGIN: loopregions -->
                                                        <option value="{REGION.key}" data-province-ids="{REGION.province_ids}" {REGION.selected}>{REGION.title}</option>
                                                        <!-- END: loopregions -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group province_sel">
                                            <label class="control-label">{LANG.idprovince}</label>
                                            <div class="row">
                                                <div class="col-xs-24">
                                                    <select class="form-control fselect2 selected_phanmuc selProvinces"
                                                            id="idprovince" name="idprovince[]" multiple="multiple" style="width: 100%">
                                                        <!-- BEGIN: loopidprovince -->
                                                        <option data-alias="{PROVINCE.alias}" value="{PROVINCE.key}"{PROVINCE.selected} >{PROVINCE.title}</option>
                                                        <!-- END: loopidprovince -->
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label">{LANG.moneybid}</label> <input class="money" type="hidden" name="money_from" value="{MONEY_FROM}" data-default="" />
                                        </div>
                                        <div class="form-group col-md-12 pdl0">
                                            <div class="input-group ">
                                                <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="money_from" type="text" data-name="money_from" value="{MONEY_FROM_FORMAT}" data-default="" maxlength="20" />
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12 pdl0">
                                            <input class="money" type="hidden" name="money_to" value="{MONEY_TO}" data-default="" />
                                            <div class="input-group">
                                                <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="money_to" type="text" data-name="money_to" value="{MONEY_TO_FORMAT}" data-default="" maxlength="20" />
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label">{LANG.price_bid}</label> <input class="money" type="hidden" name="price_from" value="{PRICE_FROM}" data-default="" />
                                        </div>
                                        <div class="form-group col-md-12 pdl0">
                                            <div class="input-group">
                                                <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="tbmt_price_from" type="text" data-name="price_from" value="{PRICE_FROM_FORMAT}" data-default="" maxlength="20" />
                                            </div>
                                        </div>
                                        <div class="form-group col-md-12 pdl0">
                                            <input class="money" type="hidden" name="price_to" value="{PRICE_TO}" data-default="" />
                                            <div class="input-group">
                                                <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="tbmt_price_to" type="text" data-name="price_to" value="{PRICE_TO_FORMAT}" data-default="" maxlength="20" />
                                            </div>
                                        </div>

                                        <div class="form-group type_org"
                                            <!-- BEGIN: type_org_hide -->
                                            style="display: none"
                                            <!-- END: type_org_hide -->
                                            > <label class="control-label">{LANG.type_org}</label>
                                            <!-- BEGIN: org -->
                                            <label class="custom-radio"> <input type="radio" name="type_org" onchange="bl_setAliasAction()" value="{ORG.key}" data-default="{TYPE_ORG_DEFAULT}" data-action="{ORG.action}"{ORG.checked}><span class="txt">{ORG.title}</span>
                                            </label>
                                            <!-- END: org -->
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="form-group ls_goods province_sel"
                                            <!-- BEGIN: ls_goods_hide -->
                                            style="display: none"
                                            <!-- END: ls_goods_hide -->
                                            > <label class="control-label">{LANG.goods_search}</label>
                                            <!-- BEGIN: goods -->
                                            <label class="custom-radio"> <input type="radio" name="goods" value="{GOODS.key}" data-default="0"{GOODS.checked}><span class="txt">{GOODS.title}</span>
                                            </label>
                                            <!-- END: goods -->
                                        </div>
                                        <div class="form-group ls_cat search_tbmt"
                                            <!-- BEGIN: ls_cat_hide -->
                                            style="display: none"
                                            <!-- END: ls_cat_hide -->
                                            > <label class="control-label">{LANG.cat}</label>
                                            <!-- BEGIN: cat -->
                                            <label class="custom-radio"> <input type="radio" name="cat" value="{CAT.key}" onchange="bl_setAliasAction()" data-default="{CAT_DEFAULT}" data-action="{CAT.action}"{CAT.checked}><span class="txt">{CAT.title}</span>
                                            </label>
                                            <!-- END: cat -->
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label">{LANG.field_msc}</label>
                                            <div class="">
                                                <!-- BEGIN: field -->
                                                <div id="linh_vuc_{FIELD.key}">
                                                    <label class="custom-checkbox" <!-- BEGIN: field_hide --> style="display: none"<!-- END: field_hide -->>
                                                        <input type="checkbox" id="ccb_field_block_{FIELD.key}" name="field[]" class="ccb_field" value="{FIELD.key}" data-default="false"{FIELD.checked}>
                                                        <span class="txt">{FIELD.title}</span>
                                                    </label>
                                                    <!-- BEGIN: show_lct --> <div id="show_lct"></div> <!-- END: show_lct -->
                                                </div>
                                                <!-- END: field -->
                                                <div class="form-group " id="searchLCT" data-toggle="tooltip" data-trigger="click" data-placement="top" title="{LANG.search_lct_note}" style="display: none;">
                                                    <label class="control-label">{LANG.search_lct}<span class="text-info" role="button"> <i class="fa fa-info-circle" aria-hidden="true"></i></span></label>
                                                    <div class="row">
                                                        <div class="col-xs-24">
                                                            <select class="form-control fselect2 selected_phanmuc" name="idlct[]" multiple="multiple" style="width: 100%" disabled>
                                                                <!-- BEGIN: loop_lct -->
                                                                <option value="{LOAICONGTRINH.key}"{LOAICONGTRINH.selected}>{LOAICONGTRINH.title}</option>
                                                                <!-- END: loop_lct -->
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label">{LANG.phuong_thuc}</label>
                                            <div class="">
                                                <!-- BEGIN: phuongthuc -->
                                                <div id="phuong_thuc_{PHUONGTHUC.key}">
                                                    <label class="custom-checkbox">
                                                        <input type="checkbox" id="phuong_thuc_{PHUONGTHUC.key}" name="phuong_thuc[]" class="phuong_thuc" value="{PHUONGTHUC.key}" data-default="false" {PHUONGTHUC.checked}>
                                                        <span class="txt">{PHUONGTHUC.title}</span>
                                                    </label>
                                                </div>
                                                <!-- END: phuongthuc -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="search_khlcnt"
                                <!-- BEGIN: no_khlcnt -->
                                style="display: none"
                                <!-- END: no_khlcnt -->
                                >
                                <!-- BEGIN: show_province_plan -->
                                <div class="form-group fg_selRegions_kh">
                                    <label class="control-label">{LANG.idregion}</label>
                                    <div class="row">
                                        <div class="col-xs-24">
                                            <select data-selected="{SREGION}" class="form-control selRegions_kh" onchange="bl_changeLocationRegionPlan()"
                                                    name="idregion_plan" style="width: 100%" data-default="0">
                                                <!-- BEGIN: loopregions_plan -->
                                                <option value="{REGION.key}" data-province-ids="{REGION.province_ids}" {REGION.selected}>{REGION.title}</option>
                                                <!-- END: loopregions_plan -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group fg_selProvinces_kh">
                                    <label class="control-label">{LANG.idprovince}</label>
                                    <div class="row">
                                        <div class="col-xs-24">
                                            <select class="form-control fselect2 selected_phanmuc selProvinces_kh"
                                                    id="idprovince_kh" name="idprovince_plan[]" multiple="multiple" style="width: 100%">
                                                <!-- BEGIN: loopidprovince_plan -->
                                                <option value="{PROVINCE.key}"{PROVINCE.selected}>{PROVINCE.title}</option>
                                                <!-- END: loopidprovince_plan -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <!-- END: show_province_plan -->

                                <div class="form-group">
                                    <label class="control-label">{LANG.total_invest}</label>
                                </div>
                                <div class="form-group col-md-12 pdl0">
                                    <input class="money" type="hidden" name="invest_from" value="{INVEST_FROM}" data-default="" />
                                    <div class="input-group">
                                        <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="invest_from" type="text" data-name="invest_from" value="{INVEST_FROM_FORMAT}" data-default="" maxlength="20" />
                                    </div>
                                </div>
                                <div class="form-group col-md-12 pdl0">
                                    <input class="money" type="hidden" name="invest_to" value="{INVEST_TO}" data-default="" />
                                    <div class="input-group">
                                        <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="invest_to" type="text" data-name="invest_to" value="{INVEST_TO_FORMAT}" data-default="" maxlength="20" />
                                    </div>
                                </div>
                                <div class="price_contract"
                                    <!-- BEGIN: price_contract_hide -->
                                    style="display: none"
                                    <!-- END: price_contract_hide -->
                                    >
                                    <div class="form-group">
                                        <label class="control-label">{LANG.price_contract}</label>
                                    </div>
                                    <div class="form-group col-md-12 pdl0">
                                        <input class="money" type="hidden" name="price_plan_from" value="{PRICE_PLAN_FROM}" data-default="" />
                                        <div class="input-group">
                                            <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="contract_price_plan_from" type="text" data-name="price_plan_from" value="{PRICE_PLAN_FROM_FORMAT}" data-default="" maxlength="20" />
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12 pdl0">
                                        <input class="money" type="hidden" name="price_plan_to" value="{PRICE_PLAN_TO}" data-default="" />
                                        <div class="input-group">
                                            <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="contract_price_plan_to" type="text" data-name="price_plan_to" value="{PRICE_PLAN_TO_FORMAT}" data-default="" maxlength="20" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="search_dau_gia"
                                <!-- BEGIN: no_dau_gia -->
                                style="display: none"
                                <!-- END: no_dau_gia -->
                                >
                                <div class="form-group" id="gr_organization">
                                    <label class="control-label">{LANG.organization}</label>
                                    <select name="keyword_id_bidder" class="form-control" id="keyword_id_bidder" style="width: 100%" data-default="0">
                                        <option value="0">Tất cả</option>
                                        <!-- BEGIN: bidder -->
                                        <option value="{BIDDER.key}"{BIDDER.selected}>{BIDDER.name_bidder}</option>
                                        <!-- END: bidder -->
                                    </select>
                                </div>
                                <div class="form-group" id="gr_price">
                                    <label class="control-label">{LANG.price_dau_gia}</label>
                                    <div class="form-group col-md-12 pdl0">
                                        <input class="money" type="hidden" name="keyword_min_bid_prices" value="{keyword_min_bid_prices}" data-default="" />
                                        <div class="input-group">
                                            <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="keyword_min_bid_prices" type="text" data-name="keyword_min_bid_prices" value="{keyword_min_bid_prices_format}" data-default="" maxlength="17" />
                                        </div>
                                    </div>
                                    <div class="form-group col-md-12 pdl0">
                                        <input class="money" type="hidden" name="keyword_max_bid_prices" value="{keyword_max_bid_prices}" data-default="" />
                                        <div class="input-group">
                                            <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="keyword_max_bid_prices" type="text" data-name="keyword_max_bid_prices" value="{keyword_max_bid_prices_format}" data-default="" maxlength="17" />
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group" id="gr_location">
                                    <label class="control-label">{LANG.location}</label>
                                    <div class="form-group col-md-12 pdl0">
                                        <label class="control-label">{LANG.idprovince}: </label>
                                        <select data-selected="{SPROVINCE}" name="keyword_id_province" id="keyword_id_province" class="form-control  fselect2" style="width: 100%" data-default="{LANG.idprovince}">
                                            <option value="-1">{LANG.idprovince}</option>
                                            <!-- BEGIN: province -->
                                            <option value="{PROVINCE.key}"{PROVINCE.selected}>{PROVINCE.title}</option>
                                            <!-- END: province -->
                                        </select>
                                    </div>
                                    <div class="form-group col-md-12 pdl0">
                                        <label class="control-label">{LANG.iddistrict}: </label>
                                        <select data-selected="{SDISTRICT}" name="keyword_id_district" id="keyword_id_district" class="form-control fselect2" style="width: 100%" data-default="{LANG.iddistrict}" data-timestamp="{TIMESTAMP}">
                                            <option value="0">{LANG.iddistrict}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="search_devproject"
                                <!-- BEGIN: no_devproject -->
                                style="display: none"
                                <!-- END: no_devproject -->
                                >
                                <div class="form-group">
                                    <label class="control-label">{LANG.total_invest}</label>
                                </div>
                                <div class="form-group col-sm-12 col-md-12 pdl0">
                                    <input class="money" type="hidden" name="invest_from" value="{INVEST_FROM}" data-default="" />
                                    <div class="input-group">
                                        <span class="input-group-addon"><span>{LANG.from}:</span></span> <input class="form-control money-format" id="dev_invest_from" type="text" data-name="invest_from" value="{INVEST_FROM_FORMAT}" data-default="" maxlength="20" />
                                    </div>
                                </div>
                                <div class="form-group col-sm-12 col-md-12 pdl0">
                                    <input class="money" type="hidden" name="invest_to" value="{INVEST_TO}" data-default="" />
                                    <div class="input-group">
                                        <span class="input-group-addon"><span>{LANG.to}:</span></span> <input class="form-control money-format" id="dev_invest_to" type="text" data-name="invest_to" value="{INVEST_TO_FORMAT}" data-default="" maxlength="20" />
                                    </div>
                                </div>
                                <div class="form-group col-sm-12 col-md-12 pdl0">
                                    <label class="control-label">{LANG.ODA}</label>
                                    <select class="form-control" id="type_info"  name="oda" data-default="0">
                                        <!-- BEGIN: oda_type -->
                                        <option value="{oda_type.key}"{oda_type.selected}>{oda_type.title}</option>
                                        <!-- END: oda_type -->
                                    </select>
                                </div>
                                <div class="form-group col-sm-12 col-md-12 pdl0 khlcnt_type" <!-- BEGIN: hide_khlcnt_type -->style="display: none"<!-- END: hide_khlcnt_type -->>
                                    <label class="control-label">{LANG.khlcnt}</label>
                                    <select class="form-control" id="type_khlcnt"  name="khlcnt" data-default="0">
                                        <!-- BEGIN: khlcnt_type -->
                                        <option value="{khlcnt_type.key}"{khlcnt_type.selected}>{khlcnt_type.title}</option>
                                        <!-- END: khlcnt_type -->
                                    </select>
                                </div>
                            </div>

                            <div class="search_ycbg" <!-- BEGIN: no_ycbg -->
                                style="display: none"
                                <!-- END: no_ycbg -->>
                                <div class="col-sm-15 col-md-15" style="padding-left: 0">
                                    <div class="form-group">
                                        <label class="control-label">{LANG.rq_investor} <span data-toggle="tooltip" data-original-title="{LANG.note_investor}"><i class="fa fa-info-circle" aria-hidden="true"></i></span> </label>
                                        <div class="row">
                                            <div class="col-xs-24">
                                                <select class="form-control" id="rq_investor" multiple="multiple" name="rq_investor[]" style="width: 100%">
                                                    <!-- BEGIN: loop_ycbg -->
                                                    <option value="{RQ_INVESTOR.key}"{RQ_INVESTOR.selected}>{RQ_INVESTOR.title}</option>
                                                    <!-- END: loop_ycbg -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-15 col-md-15" style="padding-left: 0">
                                        <label class="control-label">{LANG.diadiem}</label>
                                        <div class="form-group">
                                            <div class="location-selects">
                                                <div class="row" style="margin-bottom: 5px;">
                                                    <div class="col-xs-24">
                                                        <div class="input-group">
                                                            <span class="input-group-addon"><i class="fa fa-map-marker"></i></span>
                                                            <select class="form-control select2-location" name="rq_province" id="rq_province" data-selected="{RQ_PROVINCE}" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}" data-no_title="{LANG.no_title}">
                                                                <option value="-1">{LANG.pleaseselect}</option>
                                                                <option value="0">-- {LANG.no_title} --</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row" id="district_container" style="display: none; margin-bottom: 5px;">
                                                    <div class="col-xs-24">
                                                        <div class="input-group">
                                                            <span class="input-group-addon"><i class="fa fa-building-o"></i></span>
                                                            <select class="form-control select2-location" name="rq_district" id="rq_district" data-selected="{RQ_DISTRICT}" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}"  data-no_title="{LANG.no_title}">
                                                                <option value="0">-- {LANG.pleaseselect} --</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row" id="ward_container" style="display: none;">
                                                    <div class="col-xs-24">
                                                        <div class="input-group">
                                                            <span class="input-group-addon"><i class="fa fa-home"></i></span>
                                                            <select class="form-control select2-location" name="rq_ward" id="rq_ward" data-selected="{RQ_WARD}" data-default="{LANG.pleaseselect}" data-timestamp="{TIMESTAMP}" data-no_title="{LANG.no_title}">
                                                                <option value="0">-- {LANG.pleaseselect} --</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-9 col-md-9">
                                        <div class="form-group rq_form_value"
                                            <!-- BEGIN: hide_rq_form_value -->
                                            style="display: none"
                                            <!-- END: hide_rq_form_value -->>
                                            <label class="control-label">{LANG.rq_form_value_search}</label>
                                            <!-- BEGIN: rq_form_value -->
                                            <label class="custom-radio"> <input type="radio" name="rq_form_value" value="{FORM_VALUE.key}" data-default="0"{FORM_VALUE.checked}><span class="txt">{FORM_VALUE.title}</span>
                                            </label>
                                            <!-- END: rq_form_value -->
                                        </div>
                                    </div>    
                                </div>
                            </div>

                            <div class="row form-group text-center">
                                <button class="btn btn-primary submit_search" type="button" onclick="$('#bltablesearch{BLOCKID}').submit();">{LANG.search}</button>
                                <input class="btn btn-default form-reset" type="button" value="{GLANG.reset}" onclick="bl_formReset();" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row margin-bottom visible-md-block visible-lg-block visible-xl-block">
                    <div class="col-xs-24 help">
                        <a href="javascript:void(0);" onclick="bl_formReset();"><em class="icon-arrow-right"></em><span>{GLANG.reset}</span></a>
                        <a <!-- BEGIN: hide_guide_search_1 -->
                            style="display: none"
                            <!-- END: hide_guide_search_1 -->
                            href="{NV_BASE_SITEURL}{LANG.link_page_search}"><em class="icon-arrow-right"></em>{LANG.search_help}</a>
                        <a href="{LANG_DOCUMENT_URL}"><em class="icon-arrow-right"></em>{LANG_DOCUMENT_TEXT}</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row margin-bottom hidden-md hidden-lg hidden-xl">
            <div class="col-xs-24 help">
                <a href="javascript:void(0);" onclick="bl_formReset();"><em class="icon-arrow-right"></em><span>{GLANG.reset}</span></a>
                <a <!-- BEGIN: hide_guide_search_2 -->
                    style="display: none"
                    <!-- END: hide_guide_search_2 -->
                    href="{NV_BASE_SITEURL}{LANG.link_page_search}"><em class="icon-arrow-right"></em>{LANG.search_help}</a>
                <a href="{LANG_DOCUMENT_URL}"><em class="icon-arrow-right"></em>{LANG_DOCUMENT_TEXT}</a>
            </div>
        </div>
        <input type="hidden" name="searching" value="1" />
    </form>
</div>
<style>
    .tag__search {
        font-weight: 700;
    }

    /* Cải thiện giao diện chọn địa điểm */
    .location-selects {
        margin-top: 5px;
    }

    .location-selects .input-group {
        width: 100%;
    }

    .location-selects .input-group-addon {
        background-color: #f8f8f8;
        border-color: #ddd;
        color: #666;
    }

    .location-selects .select2-container--default .select2-selection--single {
        border-color: #ddd;
        height: 34px;
    }

    .location-selects .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 32px;
        padding-left: 12px;
    }

    .location-selects .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 32px;
    }

    .select2-dropdown {
        border-color: #ddd;
    }

    .select2-container--open .select2-dropdown--below {
        border-top: 0;
        box-shadow: 0 6px 12px rgba(0,0,0,.175);
    }
</style>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript">
    var bodycontent = $("[id*=bodycontent]");
    $(function() {
        $('#keyword_id_bidder').select2({
            language : nv_lang_interface,
            minimumInputLength : 2, multiple : false,
            ajax : {
                url : nv_base_siteurl + 'index.php?' + nv_name_variable + '=dau-gia&' + nv_fc_variable + '=organization&get_organ=1&nocache=' + new Date().getTime(),
                dataType : 'json',
                delay : 250, // wait 250  milliseconds before triggering the request
                data : function(term, page) {
                    return { q : term, page_limit : 20, page : page
                    //you need to send page number or your script do not know witch results to skip
                    };
                },
                results : function(data, page) {
                    var more = (page * 20) < data.total;
                    return { results : data.results, more : more };
                },
                dropdownCssClass : "bigdrop"
            }
        });
        $('#select2-keyword_id_bidder-container').on('click', function() {
            if ($('#select2-keyword_id_bidder-container').text() != '{LANG.all}') {
                console.log($('#select2-keyword_id_bidder-container').text());
                $('[aria-controls=select2-keyword_id_bidder-results]').val($('#select2-keyword_id_bidder-container').text());
                $('[aria-controls=select2-keyword_id_bidder-results]').focus();
                $('[aria-controls=select2-keyword_id_bidder-results]').trigger('input');
            }
        });
        var url = window.location.href;
        res = url.indexOf("bcontent");
        if (res > 0) {
            $('html, body').animate({ scrollTop : bodycontent.offset().top + 500 }, 800);
        }
    });
    // Issue 1862: Nếu chọn lĩnh vực xây lắp thì mới hiển thị chọn Loại Công Trình
    $(document).ready(function() {
        var selectField = $('#searchLCT select');
        var checkboxField = $('input[name="field[]"][value="2"]');
        var showLctDiv = $('#show_lct');
        $('#searchLCT').tooltip('hide');

        // Kiểm tra trạng thái của checkbox khi trang được tải lại
        var isField2Checked = checkboxField.prop('checked');
        if (isField2Checked) {
            $('#searchLCT').appendTo(showLctDiv).show();
            selectField.prop('disabled', false);
        } else {
            $('#searchLCT').hide().tooltip('hide');
            selectField.prop('disabled', true);
            selectField.val([]).trigger('change');
        }

        // Xử lý sự kiện thay đổi của checkbox
        $('input[name="field[]"]').change(function() {
            isField2Checked = checkboxField.prop('checked');
            if (isField2Checked) {
                $('#searchLCT').appendTo(showLctDiv).show();
                selectField.prop('disabled', false);
            } else {
                $('#searchLCT').hide().tooltip('hide');
                selectField.prop('disabled', true);
                selectField.val([]).trigger('change');
            }
        });
        $('[data-toggle="tooltip"]').tooltip();

        $('#rq_investor').select2({
            tags: true,
            placeholder: '',
            allowClear: true,
            createTag: function(params) {
                // Kiểm tra nếu giá trị của thẻ không được rỗng
                if ($.trim(params.term) === '') {
                    return null; // Không tạo thẻ nếu giá trị rỗng
                }

                // Kiểm tra độ dài của thẻ không vượt quá 200 ký tự
                if (params.term.length > 200) {
                    alert('{LANG.rq_investor_length_alert}');
                    return null; // Ngăn không cho tạo thẻ nếu vượt quá giới hạn
                }

                // Kiểm tra nếu đã có 5 thẻ được tạo
                var existingTags = $('#rq_investor').select2('data');
                if (existingTags.length >= 5) {
                    alert('{LANG.rq_investor_max_num_alert}');
                    return null; // Ngăn không cho tạo thêm thẻ
                }

                return {
                    id: params.term,
                    text: params.term,
                    newTag: true
                };
            }
        });

        window.keywordNote = {
            // Dự án
            7: '{LANG.project_keynote}',

            // KHTTLCNT
            15: '{LANG.khttlcnt_keynote}',

            // KHLCNT
            2: '{LANG.khlcnt_keynote}',

            // TBMST
            4: '{LANG.other_keynote}',

            // TBMQT
            12: '{LANG.other_keynote}',

            // KQSTNT
            6: '{LANG.other_keynote}',

            // KQMQTNT
            13: '{LANG.other_keynote}',

            // TBMT
            1: '{LANG.main_keynote}',

            // KQMT
            5: '{LANG.kqmt_keynote}',

            // KQLCNT
            3: '{LANG.kqlcnt_keynote}',

            // KQMST
            10: '{LANG.other_keynote}',

            // KQMQT
            14: '{LANG.other_keynote}',

            // YCBG
            17: '{LANG.other_keynote}',

            // OTHER
            0: '{LANG.other_keynote}',

        };

        window.ndtKeywordNote = {
            // Công bố danh mục dự án
            1: '{LANG.ndt_cbdmda_keynote}',

            // Thông báo khảo sát sự quan tâm
            8: '{LANG.ndt_tbkssqt_keynote}',

            // Kế hoạch lựa chọn nhà đầu tư
            4: '{LANG.ndt_khlcndt_keynote}',

            // Thông báo mời sơ tuyển/quan tâm nhà đầu tư
            3: '{LANG.other_keynote}',

            // Kết quả sơ tuyển nhà đầu tư
            6: '{LANG.other_keynote}',

            // Thông báo mời đầu tư
            2: '{LANG.ndt_tbmdt_keynote}',

            // Kết quả lựa chọn nhà đầu tư
            5: '{LANG.ndt_kqlcndt_keynote}',

            // OTHER
            0: '{LANG.other_keynote}',
        };

        window.daugiaKeywordNote = {
            // Thông báo công khai việc đấu giá
            1: '{LANG.daugia_congkhai_viec_keynote}',

            // Thông báo lựa chọn tổ chức đấu giá
            2: '{LANG.daugia_luachon_tochuc_keynote}',
        };

        // Xử lý trường hợp nếu vào trực tiếp một trang có chứa form search
        if ( !$('body').hasClass("mf-home") ) {
             /*
                NOTE: Không gọi 2 hàm bl_changeTypeInfo() và bl_changeTypeInfo2() ở đây
                vì trong 2 hàm này có thể chứa các logic khác (ví dụ hàm reset_validate() )
                dẫn đến kết quả không mong muốn
            */
            if ( $("[name=type_info]").css('display') == 'block' ) {
                // bl_changeTypeInfo();
                var type = parseInt($("[name=type_info]", formObject).val());
                if (keywordNote[type]) {
                    $('#main_keynote').attr('data-original-title', keywordNote[type]);
                } else {
                    $('#main_keynote').attr('data-original-title', keywordNote[0]);
                }
            }
            if ( $("[name=type_info2]").css('display') == 'block' ) {
                // bl_changeTypeInfo2();
                var type2 = parseInt($("[name=type_info2]", formObject).val()), is_advance = parseInt($('input[name="is_advance"]').val());
                if (ndtKeywordNote[type2]) {
                    $('#main_keynote').attr('data-original-title', ndtKeywordNote[type2]);
                } else {
                    $('#main_keynote').attr('data-original-title', ndtKeywordNote[0]);
                }
            }
        }
    });
</script>
<!-- BEGIN: show_typeinfo3_js -->
<script type="text/javascript">
    $(function() {
        bl_changeTypeInfo3();
    });
</script>
<!-- END: show_typeinfo3_js -->
<!-- END: main -->
