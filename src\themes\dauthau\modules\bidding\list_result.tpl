<!-- BEGIN: main -->
<!-- BEGIN: title_type1 -->
<div class="border-bidding" id="bodycontent">
    <h2 class="title__tab_heading"><span>{PAGE_TITLE_1}</span></h2>
</div>
<!-- END: title_type1 -->
<!-- BEGIN: title_type2 -->
<div class="border-bidding" id="bodycontent">
    <h2 class="title__tab_heading"><span>{LANG.ket_qua_lcndt}</span></h2>
</div>
<!-- END: title_type2 -->
<!-- BEGIN: empty -->
<div class="alert alert-warning">{LANG.empty_result}</div>
<!-- END: empty -->
<!-- BEGIN: add_filter -->
<div class="alert alert-success">{LINK_ADD}</div>
<!-- END: add_filter -->
<!-- BEGIN: error_money -->
<div class="alert alert-danger">{ERROR_MONEY}</div>
<!-- END: error_money -->
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<!-- BEGIN: view -->
<form action="{NV_BASE_SITEURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <table class="bidding-table">
        <thead>
            <tr>
                <th>{LANG.goi_thau}</th>
                <th>{LANG.ben_moi_thau}</th>
                <th>{LANG.finish_time_sort}</th>
                <th>{LANG.win_bidder_sort}</th>
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="order-header" data-column="{LANG.goi_thau}">
                    <div>
                        <!-- BEGIN: tbmt_yes_link -->
                        <a title="{VIEW.title}" href="{VIEW.link_tbmt}"><span class="bidding-code">{VIEW.code}</span>{VIEW.title}</a>
                        <!-- END: tbmt_yes_link -->
                        <!-- BEGIN: tbmt_no_link -->
                        <span class="bidding-code">{VIEW.code}</span>{VIEW.title}
                        <!-- END: tbmt_no_link -->
                    </div>
                </td>
                <td data-column="{LANG.ben_moi_thau}">
                    <div>
                        <!-- BEGIN: solicitor_yes_link -->
                        <a title="{VIEW.solicitor_title}" href="{VIEW.link_solicitor}"> <!-- BEGIN: solicitor_code --> <span class="solicitor-code">{VIEW.solicitor_code}</span> <!-- END: solicitor_code -->{VIEW.solicitor_title}
                        </a>
                        <!-- END: solicitor_yes_link -->
                        <!-- BEGIN: solicitor_no_link -->
                        <!-- BEGIN: solicitor_code -->
                        <span class="solicitor-code">{VIEW.solicitor_code}</span>
                        <!-- END: solicitor_code -->
                        {VIEW.solicitor_title}
                        <!-- END: solicitor_no_link -->
                    </div>
                </td>
                <td class="txt-center" data-column="{LANG.finish_time}"><div>{VIEW.finish_time}</div></td>
                <td data-column="{LANG.win_bidder}">
                    <div>
                        <div class="margin-bottom">
                            <!-- BEGIN: bidder -->
                            <ul class="list-bidder list-bidder-scroll">
                                <!-- BEGIN: yes_link -->
                                <li><a href="{BIDDER.link}"> <!-- BEGIN: caret --> <em class="fa fa-caret-right">&nbsp;</em> <!-- END: caret --> <!-- BEGIN: licence --> <span class="lic-code">{BIDDER.no_business_licence} </span> <!-- END: licence --><!-- BEGIN: vip --> <span class="vip-code" title="{BIDDER.vip_list}">VIP</span> <!-- END: vip -->{BIDDER.name}
                                </a></li>
                                <!-- END: yes_link -->
                                <!-- BEGIN: no_link -->
                                <li>
                                    <!-- BEGIN: caret --> <em class="fa fa-caret-right">&nbsp;</em> <!-- END: caret --> <!-- BEGIN: licence --> <span class="lic-code">{BIDDER.no_business_licence} </span> <!-- END: licence --><!-- BEGIN: vip --> <span class="vip-code" title="{BIDDER.vip_list}">VIP</span> <!-- END: vip -->{BIDDER.name}
                                </li>
                                <!-- END: no_link -->
                            </ul>
                            <!-- END: bidder -->
                            <a href="javascript:void(0)" class="loadMoreBtn"><span>{LANG.view_more}<span class="remainingCount" data-nhathau="{LANG.nhathau}"></span> </i><i class="fa fa-caret-down" aria-hidden="true"></i></span></a>
                            <div class="spinner hidden"><i class="fa fa-spinner fa-spin fa-fw" aria-hidden="true"></i> {LANG.loading}</div>
                        </div>
                        <div class="last">
                            <div class="text-right">
                                <a class="btn btn-primary btn-xs" href="{VIEW.link}"><em class="fa fa-arrow-circle-right">&nbsp;</em>{LANG.kq_detail}</a>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>
    <!-- BEGIN: generate_page -->
    <div class="text-center">{NV_GENERATE_PAGE}</div>
    <!-- END: generate_page -->
</form>
<!-- END: view -->
<!-- BEGIN: bids_result_dtnet -->
<div class="alert alert-success">{LANG.goi_y_goi_thau_dtnet}</div>
<table class="bidding-table">
    <thead>
    <tr>
        <th>{LANG.goi_thau}</th>
        <th>{LANG.ben_moi_thau}</th>
        <th>{LANG.finish_time_sort}</th>
        <th>{LANG.win_bidder_sort}</th>
    </tr>
    </thead>
    <tbody>
    <!-- BEGIN: loop -->
    <tr>
        <td class="order-header" data-column="{LANG.goi_thau}">
            <div class="wrap__text">
                <a title="{RESULT_DTNET.title_inform}" href="{RESULT_DTNET.link_inform}"><span class="bidding-code">{RESULT_DTNET.bid_code}</span>{RESULT_DTNET.title_inform}</a>
            </div>
        </td>
        <td data-column="{LANG.ben_moi_thau}">
            <div>
                <!-- BEGIN: link_invector -->
                <a title="{RESULT_DTNET.prof_name_profile}" href="{RESULT_DTNET.link_bid_invitation}"> <!-- BEGIN: invector_code --> <span class="solicitor-code">{RESULT_DTNET.prof_code_profile}</span> <!-- END: invector_code --> {RESULT_DTNET.prof_name_profile}
                </a>
                <!-- END: link_invector -->
                <!-- BEGIN: no_link_invector -->
                {RESULT_DTNET.prof_name_profile}
                <!-- END: no_link_invector -->
            </div>
        </td>
        <td class="txt-center" data-column="{LANG.ngay_dang_tai}"><div>{RESULT_DTNET.create_time}</div></td>
        <td data-column="{LANG.trung_thau}">
            <div>
                <div class="margin-bottom">
                    <p class="mb-2">
                        {RESULT_DTNET.status_text}
                    </p>
                </div>
                <div class="last">
                    <div class="text-right">
                        <a class="btn btn-primary btn-xs" href="{RESULT_DTNET.link_result}"><em class="fa fa-arrow-circle-right">&nbsp;</em>{LANG.kq_detail}</a>
                    </div>
                </div>
            </div>
        </td>
    </tr>
    <!-- END: loop -->
    </tbody>
</table>
<div class="text-center"><a class="btn btn-primary" href="{RESULT_DTNET.link_see_more}">{LANG.see_more}</a></div>
<!-- END: bids_result_dtnet -->
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/js/loadMorekqlcnt.js"></script>
<script type="text/javascript">
    initKQLCNT();
</script>
<!-- END: main -->
