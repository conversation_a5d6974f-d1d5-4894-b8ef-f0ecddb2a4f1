<!-- BEGIN: main -->
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css">
<!-- BEGIN: js -->
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/{BLOCK_JS}/js/{MODULE_FILE}.js"></script>
<!-- END: js -->
<!-- BEGIN: css -->
<link type="text/css" rel="stylesheet" href="{NV_BASE_SITEURL}themes/{BLOCK_CSS}/css/{MODULE_FILE}.css" />
<!-- END: css -->
<form id="ltablesearch{BLOCKID}" action="{ACTION}" method="get" onsubmit="return nv_check_search_bidding(this);">
  <!-- BEGIN: no_rewrite -->
  <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
  <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
  <input type="hidden" name="{NV_OP_VARIABLE}" value="search" />
  <!-- END: no_rewrite -->
  <input type="hidden" name="is_advance" value="{IS_ADVANCE}" />
  <div class="form-group">
    <label class="control-label bidding-label">{LANG.type_search}</label> <select class="form-control" id="type_search" onchange="change_type_search()" name="type_search">
      <option value="1"{type_search1}>{LANG.type_search1}</option>
      <option value="2"{type_search2}>{LANG.type_search2}</option>
    </select>
  </div>
  <div class="form-group">
    <label class="control-label bidding-label">{LANG.type_info}</label> <select class="form-control" id="type_info" onchange="change_type_info()" name="type_info">
      <!-- BEGIN: type_info -->
      <option value="{TYPE.key}"{TYPE.selected}>{TYPE.title}</option>
      <!-- END: type_info -->
    </select> <select class="form-control" id="type_info2" onchange="change_type_info2()" name="type_info2">
      <!-- BEGIN: type_info2 -->
      <option value="{TYPE2.key}"{TYPE2.selected}>{TYPE2.title}</option>
      <!-- END: type_info2 -->
    </select>
  </div>
  <div class="form-group">
    <label class="control-label bidding-label">{LANG.s_key}</label>
    <div class="row">
      <div class="col-xs-24">
        <input class="form-control" id="ls_key_bidding" type="text" name="q" value="{Q}" />
      </div>
    </div>
  </div>
  <div>
    <div class="form-group form-inline col-md-12">
      <span class="input-group-btn icon-datepicker"> <a href="#" id="from_time_btn"><em class="fa fa-calendar fa-fix">&nbsp;</em></a>
      </span>
      <input class="bidding-control" id="ls_from_bidding" style="width: 85px" type="text" name="sfrom" value="{FROM}" readonly="readonly" />
    </div>
    <div class="form-group form-inline col-md-12">
      <span class="input-group-btn icon-datepicker"> <a href="#" id="to_time_btn"><em class="fa fa-calendar fa-fix">&nbsp;</em></a>
      </span>
      <input class="bidding-control" id="ls_to_bidding" style="width: 85px" type="text" name="sto" value="{TO}" readonly="readonly" />
    </div>
  </div>
  <div id="advance_bidding"
    <!-- BEGIN: is_advance -->
    style="display: none"
    <!-- END: is_advance -->
    >
    <div id="show_key">
      <div class="form-group" id="search_type_content">
        <div class="row">
          <div class="col-xs-24">
            <input class="form-control" type="checkbox" name="search_type_content" value="1" {SEARCH_TYPE_CONTENT} />
            {LANG.search_type}
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label bidding-label">{LANG.s_key2}</label>
        <div class="row">
          <div class="col-xs-24">
            <input class="form-control" id="ls_key2" type="text" name="q2" value="{Q2}" />
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label bidding-label">{LANG.without_key}</label>
        <div class="row">
          <div class="col-xs-24">
            <input class="form-control" id="without_key" type="text" name="without_key" value="{Q1}" />
          </div>
        </div>
      </div>
      <!-- <div class="form-group">
            <label class="control-label bidding-label">{LANG.s_key2}</label>
            <div class="row">
                <div class="col-xs-24"><input class="form-control" id="ls_key2" type="text" name="k2" value="{K2}"/></div>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label bidding-label">{LANG.without_key}</label>
            <div class="row">
                <div class="col-xs-24"><input class="form-control" id="without_key" type="text" name="without_key1" value="{K1}"/></div>
            </div>
        </div>-->
    </div>
    <div id="search_tbmt"
      <!-- BEGIN: no_tbmt -->
      style="display: none"
      <!-- END: no_tbmt -->
      >
      <div class="form-group ls_cat">
        <label class="control-label bidding-label">{LANG.cat}</label> <select class="form-control" id="ls_cat_bidding" name="cat">
          <option value="0">---------</option>
          <!-- BEGIN: cat -->
          <option value="{CAT.key}"{CAT.selected}>{CAT.title}</option>
          <!-- END: cat -->
        </select>
      </div>
      <div class="form-group">
        <label class="control-label bidding-label">{LANG.field}</label>
        <div class="">
          <!-- BEGIN: field -->
          <input type="checkbox" id="ccb_field_block_{FIELD.key}" class="form-control ccb_field" name="field[]" value="{FIELD.key}"{FIELD.checked}>
          {FIELD.title}</br>
          <!-- END: field -->
        </div>
      </div>
      <div class="form-group type_org">
        <label class="control-label bidding-label">{LANG.type_org}</label>
        <!-- BEGIN: org -->
        <div class="radio">
          <input type="radio" name="type_org" value="{ORG.key}" {ORG.checked}/>
          {ORG.title}
        </div>
        <!-- END: org -->
      </div>
      <div class="form-group">
        <label class="control-label bidding-label">{LANG.moneybid}</label>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.from}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" onkeyup="this.value=FormatMoney(this.value);" id="money_from" type="text" name="money_from" value="{MONEY_FROM}" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.to}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" onkeyup="this.value=FormatMoney(this.value);" id="money_to" type="text" name="money_to" value="{MONEY_TO}" />
            </div>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label bidding-label">{LANG.price_bid}</label>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.from}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" id="price_from" onkeyup="this.value=FormatMoney(this.value);" type="text" name="price_from" value="{PRICE_FROM}" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.to}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" id="price_to" type="text" onkeyup="this.value=FormatMoney(this.value);" name="price_to" value="{PRICE_TO}" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="search_khlcnt"
      <!-- BEGIN: no_khlcnt -->
      style="display: none"
      <!-- END: no_khlcnt -->
      >
      <div class="form-group">
        <label class="control-label bidding-label">{LANG.total_invest}</label>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.from}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" id="invest_from" onkeyup="this.value=FormatMoney(this.value);" type="text" name="invest_from" value="{INVEST_FROM}" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.to}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" id="invest_to" type="text" onkeyup="this.value=FormatMoney(this.value);" name="invest_to" value="{INVEST_TO}" />
            </div>
          </div>
        </div>
      </div>
      <div class="form-group price_contract">
        <label class="control-label bidding-label">{LANG.price_contract}</label>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.from}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" id="price_plan_from" onkeyup="this.value=FormatMoney(this.value);" type="text" name="price_plan_from" value="{PRICE_PLAN_FROM}" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.to}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" id="price_plan_to" type="text" onkeyup="this.value=FormatMoney(this.value);" name="price_plan_to" value="{PRICE_PLAN_TO}" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="search_result_block"
      <!-- BEGIN: no_result -->
      style="display: none"
      <!-- END: no_result -->
      >
      <div class="form-group">
        <label class="control-label bidding-label">{LANG.cat}</label> <select class="form-control" id="ls_catressult" name="catressult">
          <option value="0">---------</option>
          <!-- BEGIN: catressult -->
          <option value="{CAT.key}"{CAT.selected_result}>{CAT.title}</option>
          <!-- END: catressult -->
        </select>
      </div>
      <div class="form-group">
        <label class="control-label bidding-label">{LANG.win_price}</label>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.from}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" id="win_price_from" onkeyup="this.value=FormatMoney(this.value);" type="text" name="win_price_from" value="{WIN_PRICE_PROM}" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-24">
            <div class="col-md-6">{LANG.to}:</div>
            <div class="col-md-18 margin-bottom">
              <input class="form-control" id="win_price_to" type="text" onkeyup="this.value=FormatMoney(this.value);" name="win_price_to" value="{WIN_PRICE_TO}" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- BEGIN: is_advance_btn -->
  <div class="search_icon col-md-24 col-sm-24 col-lg-24">
    <a class="<!-- BEGIN: is_advance_class -->advance<!-- END: is_advance_class -->"
      <!-- BEGIN: show -->style="display:none"<!-- END: show --> id="btn-search-bidding"><em class="fa fa-search">&nbsp;</em>{LANG_ADVANCE}
    </a>
  </div>
  <!-- END: is_advance_btn -->
  <div class="form-group text-center">
    <input class="btn btn-danger" id="lclearform{BLOCKID}" type="button" value="{LANG.clear}Xóa trắng" />
    <input class="btn btn-primary" type="submit" value="{LANG.search}" />
  </div>
</form>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript">
    $(document).ready(function() {
        $('#lclearform{BLOCKID}').click(function() {
            $('#ltablesearch{BLOCKID} input[type=text]').val('');
            $('#ltablesearch{BLOCKID} select').val('0');
        });
        $("#ls_from_bidding,#ls_to_bidding").datepicker({
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            yearRange : '2010:{YEAR}',
            showOn : 'focus'
        });

        $('#from_time_btn').click(function() {
            $("#ls_from_bidding").datepicker('show');
        });

        $('#to_time_btn').click(function() {
            $("#ls_to_bidding").datepicker('show');
        });

        $('#btn-search-bidding').click(function(e) {
            var type = $('#type_info').val();
            e.preventDefault();
            var a = $(this);
            if (a.hasClass('advance')) {
                if (type == 1) {
                    $('#search_khlcnt').css("display", "none");
                    $('#search_result_block').css("display", "none");
                    $('#search_tbmt').css("display", "block");
                } else if (type == 2) {
                    $('#search_khlcnt').css("display", "block");
                    $('#search_tbmt').css("display", "none");
                    $('#search_result_block').css("display", "none");
                } else {
                    $('#search_khlcnt').css("display", "none");
                    $('#search_tbmt').css("display", "none");
                    $('#search_result_block').css("display", "block");
                }
                $('#advance_bidding').slideDown();
                a.html('<em class="fa fa-search">&nbsp;</em>{LANG.search_simple}');
                a.removeClass('advance');
                $('input[name="is_advance"]').val(1);
                $('#seach_choose').css("display", "block");
            } else {
                $('#advance_bidding').slideUp();
                a.html('<em class="fa fa-search">&nbsp;</em>{LANG.search_advance}');
                a.addClass('advance');
                $('input[name="is_advance"]').val(0);

            }
        });
        $('#type_search').change(function() {
            $(".ccb_field").prop('checked', false);
        });

        change_type_search();
    });
    function change_type_search() {
        var type = $('#type_search').val();
        if (type == 2) {
            $('#type_info2').css("display", "block");
            $('#type_info').css("display", "none");

            $('#search_type_content').css("display", "none");
            change_type_info2();
        } else {
            $('#type_info').css("display", "block");
            $('#type_info2').css("display", "none");
            change_type_info();
        }

        remove_field();
    }
    function remove_field() {
        var type = $('#type_search').val();
        if (type == 2) {
            $("input[id='ccb_field_block_1']").attr("disabled", true);
            $("input[id='ccb_field_block_2']").attr("disabled", true);
            $("input[id='ccb_field_block_3']").attr("disabled", true);
            $("input[id='ccb_field_block_4']").attr("disabled", true);
            $("input[id='ccb_field_block_5']").attr("disabled", true);

            $("input[id='ccb_field_block_7']").attr("disabled", false);
            $("input[id='ccb_field_block_8']").attr("disabled", false);
            $("input[id='ccb_field_block_9']").attr("disabled", false);
            $("input[id='ccb_field_block_10']").attr("disabled", false);

            $('.type_org').css("display", "none");
            $('.ls_cat').css("display", "none");
        } else {
            $("input[id='ccb_field_block_1']").attr("disabled", false);
            $("input[id='ccb_field_block_2']").attr("disabled", false);
            $("input[id='ccb_field_block_3']").attr("disabled", false);
            $("input[id='ccb_field_block_4']").attr("disabled", false);
            $("input[id='ccb_field_block_5']").attr("disabled", false);

            $("input[id='ccb_field_block_7']").attr("disabled", true);
            $("input[id='ccb_field_block_8']").attr("disabled", true);
            $("input[id='ccb_field_block_9']").attr("disabled", true);
            $("input[id='ccb_field_block_10']").attr("disabled", true);

            $('.type_org').css("display", "block");
            $('.ls_cat').css("display", "block");
        }
    }
    function change_type_info2() {
        var type2 = $('#type_info2').val();
        var is_advance = $('input[name="is_advance"]').val();
        if (is_advance == 1) {
            $('#advance_bidding').css("display", "block");
            $('#btn-search-bidding').css("display", "block");
        } else {
            $('#advance_bidding').css("display", "none");
            $('#btn-search-bidding').css("display", "none");
        }

        if (type2 == 2) {
            $('#search_khlcnt').css("display", "none");
            $('#search_result_block').css("display", "none");
            $('#search_tbmt').css("display", "block");
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION}');
            if (is_advance == 1) {
                $('#advance_bidding').css("display", "block");
                $('#btn-search-bidding').css("display", "block");
            } else {
                $('#advance_bidding').css("display", "none");
                $('#btn-search-bidding').css("display", "block");
                $('input[name="is_advance"]').val(0);
            }
        } else if (type2 == 4) {
            $('#search_khlcnt').css("display", "block");
            $('#search_tbmt').css("display", "none");
            $('#search_result_block').css("display", "none");
            $('.price_contract').css("display", "none");
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION1}');
            if (is_advance == 1) {
                $('#advance_bidding').css("display", "block");
                $('#btn-search-bidding').css("display", "block");
            } else {
                $('#advance_bidding').css("display", "none");
                $('#btn-search-bidding').css("display", "block");
                $('input[name="is_advance"]').val(0);
            }
        } else if (type2 == 5) {
            $('#search_khlcnt').css("display", "none");
            $('#search_tbmt').css("display", "none");
            $('#search_result_block').css("display", "block");
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION2}');
            if (is_advance == 1) {
                $('#advance_bidding').css("display", "block");
                $('#btn-search-bidding').css("display", "block");
            } else {
                $('#advance_bidding').css("display", "none");
                $('#btn-search-bidding').css("display", "block");
                $('input[name="is_advance"]').val(0);
            }
        } else if (type2 == 3) {
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION3}');

            $('#advance_bidding').css("display", "none");
            $('#btn-search-bidding').css("display", "none");
        } else if (type2 == 6) {
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION4}');

            $('#advance_bidding').css("display", "none");
            $('#btn-search-bidding').css("display", "none");
        } else {
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION6}');

            $('#advance_bidding').css("display", "none");
            $('#btn-search-bidding').css("display", "none");
        }
    }
    function change_type_info() {
        var type = $('#type_info').val();
        if (type == 2) {
            $('#search_khlcnt').css("display", "block");
            $('#search_tbmt').css("display", "none");
            $('#search_result_block').css("display", "none");
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION1}');
        } else if (type == 3) {
            $('#search_khlcnt').css("display", "none");
            $('#search_tbmt').css("display", "none");
            $('#search_result_block').css("display", "block");
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION2}');
        } else if (type == 4 || type == 12) {
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION3}');
        } else if (type == 5) {
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION5}');
        } else if (type == 6) {
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION4}');
        } else {
            $('#search_khlcnt').css("display", "none");
            $('#search_result_block').css("display", "none");
            $('#search_tbmt').css("display", "block");
            $('#ltablesearch{BLOCKID}').attr('action', '{FORM_ACTION}');
        }
        if (type >= 4) {
            $('#advance_bidding').css("display", "none");
            $('#btn-search-bidding').css("display", "none");
            $('input[name="is_advance"]').val(0);
            $('#search_type_content').css("display", "none");
        } else {
            $('#btn-search-bidding').css("display", "block");
            $('#advance_bidding').css("display", "block");
            if ($('#btn-search-bidding').hasClass('advance')) {
                $('#advance_bidding').slideUp();
            }
            $('#search_type_content').css("display", "block");
            //$('input[name="is_advance"]').val(0);
        }
    }
    function nv_check_search_bidding(data) {
        if (($('#ls_key_bidding').val() == '') && ($('#tbmt').val() == 0) && ($('#ls_cat_bidding').val() == 0) && ($('.field:checked').length == 0) && ($('#type').val() == 0) && ($('#ls_from_bidding').val() == '') && ($('#ls_to_bidding').val() == '')) {
            alert('{LANG.search_alert}');
            return false;
        }
        return true;
    }
</script>
<!-- END: main -->
